# Description

# Checklist:
- [ ] My code follows the style guidelines of this project.
- [ ] My code follows the best practices that the services suggest.
- [ ] I have updated the story/task description, use case, process flow, mindmap, and data model.
- [ ] I have performed a self-review of my own code.
- [ ] I have commented on my code, particularly in hard-to-understand areas.
- [ ] I have made corresponding changes to the documentation.
- [ ] My changes should not generate new warnings.
- [ ] New and existing unit tests pass locally with my changes.
- [ ] Dependent changes have been merged and published.
- [ ] Remove Commented Code,Console.log, Unused Codes, Unnecessary Files, Hard Coded Codes.
- [ ] I have reused the functions and methods wherever it is required.
- [ ] I prepared the MySQL queries.
- [ ] I have pulled the latest code and merged it with my changes.
- [ ] Add the required packages in package.json.
- [ ] I have done the validations same in UI.
- [ ] I have sanitized all the input fields.
- [ ] I have used the try-catch to handle the functional error.
- [ ] I have handled the database error in the try-catch block if occurs.
- [ ] I have used the constant variable values from the microservice environment.
- [ ] I have used the error code to define functional and validation errors.
- [ ] I have tested the endpoint execution time which is lesser than 3 seconds.
- [ ] Response model should be the same in all places.
- [ ] I have prepared the Back-End postman test cases.