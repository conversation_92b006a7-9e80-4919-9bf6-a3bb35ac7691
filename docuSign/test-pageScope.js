// Test script to verify pageScope functionality
const { generatePDFWithPuppeteer } = require('./common/uploadDocument');

async function testPageScope() {
    console.log('Testing pageScope functionality...');
    
    const testContent = '<h1>Main Document Content</h1><p>This is the main content of the document.</p>';
    const testHeader = '<div style="text-align: center;"><h2>Document Header</h2></div>';
    const testFooter = '<div style="text-align: center;"><p>Document Footer - Page Footer</p></div>';
    
    try {
        console.log('\n1. Testing "All Pages" pageScope...');
        const allPagesPdf = await generatePDFWithPuppeteer(testContent, testHeader, testFooter, 'All Pages');
        console.log('✓ All Pages PDF generated successfully, size:', allPagesPdf.length, 'bytes');
        
        console.log('\n2. Testing "First Page Only" pageScope...');
        const firstPageOnlyPdf = await generatePDFWithPuppeteer(testContent, testHeader, testFooter, 'First Page Only');
        console.log('✓ First Page Only PDF generated successfully, size:', firstPageOnlyPdf.length, 'bytes');
        
        console.log('\n3. Testing default pageScope (should default to "All Pages")...');
        const defaultPdf = await generatePDFWithPuppeteer(testContent, testHeader, testFooter);
        console.log('✓ Default PDF generated successfully, size:', defaultPdf.length, 'bytes');
        
        console.log('\n✅ All tests passed! pageScope functionality is working correctly.');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error(error.stack);
    }
}

// Only run test if this file is executed directly
if (require.main === module) {
    testPageScope();
}

module.exports = { testPageScope };
