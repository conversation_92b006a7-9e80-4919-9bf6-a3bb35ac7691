const { ehrTables } = require('@cksiva09/hrapp-corelib/common/tablealias');

//Group the inputArray by using the group title. Sample response: 
async function groupTemplateFieldsByEntity(inputArray){
    try{
        return inputArray.reduce((tempDetails, currItem) => {
            const groupKey = currItem.entity;
            if (!tempDetails[groupKey]) {
                tempDetails[groupKey] = [currItem];
            } else {
                tempDetails[groupKey].push(currItem);
            }
            return tempDetails;
        }, {});
    }catch(entityMainCatchError){
        console.log('Error in the groupTemplateFieldsByEntity function main catch block.',entityMainCatchError);
        throw entityMainCatchError;
    }
}

//Group the inputArray by using the group title. Sample response: {'Employee Personal Info': [ 'Employee Name' ]}
async function groupTemplateFieldsByTitle(inputArray){
    try{
        return inputArray.reduce((tempDetails, currItem) => {
            const groupKey = currItem.groupTitle;
            if (!tempDetails[groupKey]) {
                tempDetails[groupKey] = [currItem.fieldName];
            } else {
                tempDetails[groupKey].push(currItem.fieldName);
            }
            return tempDetails;
        }, {});
    }catch(groupMainCatchError){
        console.log('Error in the groupTemplateFieldsByTitle function main catch block.',groupMainCatchError);
        throw groupMainCatchError;
    }
}

async function getDocumentTemplateFields(templateContent){
    try{
        let startDelimiter = '{';
        let endDelimiter = '}';
        let templateFields = [];
        let startDelimiterLength = 1;
        let endDelimiterLength = 1;
        let startDelimiterIndex = 0;
        let endDelimiterIndex = 0;
        let templateFieldNames = '';
        let templateContentLength = templateContent.length;

        for(let searchIndex=0; searchIndex<=templateContentLength;){
            //Find the index of the delimiter "{" in the template content.
            startDelimiterIndex = templateContent.indexOf(startDelimiter,searchIndex);
            //If the start delimiter does not exist
            if (startDelimiterIndex === -1) {
                break;
            }else{
                startDelimiterIndex += startDelimiterLength;
                //Find the index of the delimiter "}" in the template content starts from the previous delimiter "{"
                endDelimiterIndex = templateContent.indexOf(endDelimiter,startDelimiterIndex);
                if (endDelimiterIndex === -1) {
                    break;
                }else{
                    templateFieldNames = templateContent.slice(startDelimiterIndex,endDelimiterIndex);
                    templateFields.push(templateFieldNames);
                    searchIndex = endDelimiterIndex + endDelimiterLength;
                }
            }
        }
        return templateFields;
    }catch(catchError){
        console.log('Error in the getDocumentTemplateFields function main catch block.',catchError);
        throw catchError;
    }
}
async function updateRolledOutDate(organizationDbConnection,candiddateId){
    const moment = require('moment');
    try{
        const status = await organizationDbConnection(ehrTables.atsStatusTable)
        .select('Id').where('Status', 'Offer Letter Rolled Out').first();

        await organizationDbConnection('candidate_recruitment_info')
        .update({
            Candidate_Status: status?.Id || 25,
            Offer_Letter_Rolled_Out_Date:moment.utc().format("YYYY-MM-DD HH-MM-SS")
        })
        .where('Candidate_Id',candiddateId);
}catch(err){
    console.log('Error inside updateRolledOutDate catch block.',err)
    throw err;
}
}
async function getEmpDocumentFormId(organizationDbConnection,templateName,commonLib){
    const { ehrTables } = commonLib.tableAlias;
    let formId= await organizationDbConnection(ehrTables.documentTemplateEngine)
    .select('Form_Id as formId')
    .where('Title',templateName);

if(formId && formId.length>0){
                  if(formId[0].formId){
                      return formId[0].formId;
                  }
                  else{
                  return null;
              }
              }
              return null;
}
async function getInitiatorDetails(organizationDbConnection,documentId,commonLib){
    const { ehrTables } = commonLib.tableAlias;
    try{
    const employeeDocument =  await organizationDbConnection(ehrTables.empGeneratedDocuments + ' as EGD')
    .select(
     organizationDbConnection.raw("CONCAT_WS(' ', EPI1.Emp_First_Name, EPI1.Emp_Last_Name) as initiatorName"),
    'JP.Job_Post_Name as jobTitle','EGD.Added_By as initiatorId',
    organizationDbConnection.raw("CONCAT_WS(' ', CPI.Emp_First_Name, CPI.Emp_Last_Name) as candidateName"),)
    .leftJoin(ehrTables.empPersonalInfo +' as EPI1', 'EPI1.Employee_Id', 'EGD.Added_By')
     .leftJoin(ehrTables.candidatePersonalInfo +' as CPI', 'CPI.Candidate_Id', 'EGD.Candidate_Id')
    .leftJoin(ehrTables.candidateRecruitementInfo + " as CRI", 'CRI.Candidate_Id', 'CPI.Candidate_Id')
    .leftJoin(ehrTables.jobPost +' as JP', 'JP.Job_Post_Id', 'CRI.Job_Post_Id')

    .where('EGD.Generated_Document_Id', documentId);
    return employeeDocument;
    }catch(err){
        console.log('Error in the getInitiatorDetails function main catch block.',err);
        throw err;
    }
}
//Send an email to the authorizer
async function sendAuthorizerEmail(organizationDbConnection,emailArgs){
    try{
        let {orgCode, encryptionKey, signatureUrlPath, firstAuthorizerEmployeeId,sendEmailToCandidate,generatedDocumentId,templateName, toEmployeeEmail, toEmployeeName, documentName, sesTemplateName,candidateId,addedBy}= emailArgs;

        //Require cryptr package
        const cryptrObj = require('cryptr');
        //Get the encryption key from constant file
        const cryptr = new cryptrObj(encryptionKey);
        //Require common library to access common function
        const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
        const {awsSesTemplates}=require('./appconstants');
        const {checkFileIsExsit} = require('../common/uploadDocument');

        /** Get organization details for sending mail. Send third param as 1
         * to get the email notification related details. */
        let formId=await getEmpDocumentFormId(organizationDbConnection,templateName,commonLib);
        const encryptedString = cryptr.encrypt('authorizerId=' + firstAuthorizerEmployeeId + '&documentId=' + generatedDocumentId);

        let redirectionURL = '';
        //Form redirection URL
        if (process.env.stageName.toLowerCase() === 'local') {
            redirectionURL = 'http://localhost/in/'+signatureUrlPath+'?authorizerData=';
        } else {
            redirectionURL = 'https://' + orgCode + '.' + process.env.domainName + process.env.webAddress +'/in/'+signatureUrlPath+'?authorizerData=';
        }
        redirectionURL = redirectionURL + encryptedString;
        // Form the parameters for sending email
        let buttonColors= await commonLib.func.getButtonColor(organizationDbConnection);
        let notificationParams;

        var orgDetails = await commonLib.func.getOrgDetails(orgCode, organizationDbConnection, 1)

        let templateData={
            emailSubject: documentName+' to be signed',
            orgLogo: orgDetails.logoPath ? orgDetails.logoPath : '',
            documentContent: 'Signature request for '+toEmployeeName+' '+documentName+'.',
            redirectionUrl: redirectionURL,
            bgColors:buttonColors,
            hrappSupportEmail:orgDetails.hrAdminEmailAddress ? orgDetails.hrAdminEmailAddress :''
        }
        if(formId && (formId==16 || formId==179)){

            var organizationAddress =  [], companyName =""
        var employeeDocument=await getInitiatorDetails(organizationDbConnection,generatedDocumentId,commonLib);
                companyName = orgDetails.orgName;
                organizationAddress = await commonLib.func.getOrgAddress(organizationDbConnection,employeeDocument[0].initiatorId);
                var street1 = organizationAddress.length > 0 ? organizationAddress[0].Street1 : '';
            var street2 = organizationAddress.length > 0 ? organizationAddress[0].Street2 : '';
            var pinCode = organizationAddress.length > 0 ? organizationAddress[0].Pincode : '';
            var cityName = organizationAddress.length > 0 ? organizationAddress[0].City_Name : '';
            var stateName = organizationAddress.length > 0 ? organizationAddress[0].State_Name : '';
            var countryName = organizationAddress.length > 0 ? organizationAddress[0].Country_Name : '';
            let emailContent=`Congratulations on securing the ${employeeDocument[0].jobTitle} position at ${companyName}! `
            let templateData1={
                emailSubject: `${companyName} | Successful Job Application (${employeeDocument[0].jobTitle})`,
                orgLogo: orgDetails.logoPath ? orgDetails.logoPath : '',
                name:employeeDocument[0].candidateName?employeeDocument[0].candidateName:'',
                redirectionUrl: redirectionURL?redirectionURL:'',
                companyName:companyName?companyName:'',
                street1:street1?street1:'',
                street2:street2?street2:'',
                city: cityName?cityName:'',
                pinCode: pinCode?pinCode:'',
                state: stateName?stateName:'',
                country: countryName?countryName:'',
                recruitersName:employeeDocument[0].initiatorName?employeeDocument[0].initiatorName:'',
                emailContent:emailContent,
                hrappSupportEmail:orgDetails.hrAdminEmailAddress ? orgDetails.hrAdminEmailAddress :''
            }
            templateData1.declinedURL=redirectionURL+'&status=Declined';
            if(!sendEmailToCandidate){
                templateData.declinedURL=redirectionURL+'&status=Declined';
            }
            notificationParams = {
                'Source': process.env.emailFrom,
                'Template': sendEmailToCandidate?awsSesTemplates.offerLetterTemplate:sesTemplateName,
                'Destination': {
                    'ToAddresses': [toEmployeeEmail]
                },
                'ReplyToAddresses': orgDetails.hrAdminEmailAddress ? [orgDetails.hrAdminEmailAddress] : [],
                'TemplateData': sendEmailToCandidate?JSON.stringify(templateData1):JSON.stringify(templateData)
            };
        }
        else{
            notificationParams = {
                'Source': process.env.emailFrom,
                'Template': sesTemplateName,
                'Destination': {
                    'ToAddresses': [toEmployeeEmail]
                },
                'ReplyToAddresses': orgDetails.hrAdminEmailAddress ? [orgDetails.hrAdminEmailAddress] : [],
                'TemplateData': JSON.stringify(templateData)
            };
            templateData.declinedURL=null;
        }
        //Send an email to the authorizer
        let mailResponse = await commonLib.func.sendEmailNotifications(notificationParams, process.env.sesTemplatesRegion);
        if(mailResponse){
            if(candidateId && formId && (formId===16)){
                await updateRolledOutDate(organizationDbConnection,candidateId);
            }
            return 'success';
        }else{
            console.log('Error email notification is not sent for params: ',notificationParams);
            return 'failure';
        }
    }catch(catchError){
        console.log('Error in the sendAuthorizerEmail function main catch block.',catchError);
        return 'failure';
    }
}

/**Call this function when the signature link has to be send parallel and when the signature
 * link has to be sent to the candidate once the manager or employee signed. */
async function formSignatureEmailInputsAndMail(organizationDbConnection,emailInputs){
    try{
        //Require common library to access common function
        const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
        //Require table alias
        const { ehrTables } = commonLib.tableAlias;
        //Require moment
        const moment = require('moment');
        let {authorizerDetails,encryptionKey,orgCode,signatureUrlPath,awsSesTemplateName,generatedDocumentId,documentName,addedOn,addedBy,Template_Name,candidateId} = emailInputs;
        let sendEmailToCandidate = 0;
        let signingAuthorities = authorizerDetails;
        //Get the candidate signature details
        let candidateSignatureDetails = signingAuthorities.filter(item=>item.signatureKey=="Candidate");
        
        let signatureEmployeeIds = [];
        //If the candidate signature details exist
        if(candidateSignatureDetails.length > 0){
            //Get the signature details other than candidate
            let otherSignatureDetails = signingAuthorities.filter(item=>item.signatureKey!="Candidate");
            //If the other signature details exist
            if(otherSignatureDetails.length > 0){
                signatureEmployeeIds = [otherSignatureDetails[0].signatureEmployeeId];
            }else{
                sendEmailToCandidate = 1;
                signatureEmployeeIds = [candidateSignatureDetails[0].signatureEmployeeId];
            }
        }else{
            //If the candidate signature details not exist then signature link will be send parallel
            signingAuthorities.map((field => {
                signatureEmployeeIds.push(field.signatureEmployeeId);
            }));
        }

        //If the email has to be sent to an employee
        if(sendEmailToCandidate === 0){
            toEmployeeDetails = await organizationDbConnection
            .select('EJ.Employee_Id as uniqueId','EJ.Emp_Email as emailAddress', organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as name"))
            .from(ehrTables.empJob+' as EJ')
            .innerJoin(ehrTables.empPersonalInfo+' as EPI','EJ.Employee_Id','EPI.Employee_Id')
            .whereIn('EJ.Employee_Id',signatureEmployeeIds)
            .then(empDetails => {
                if(empDetails && empDetails.length > 0){
                    return empDetails;
                }else{
                    return [];
                }
            });
        }else{
            toEmployeeDetails = await organizationDbConnection
            .select('CPI.Candidate_Id as uniqueId','CPI.Personal_Email as emailAddress',organizationDbConnection.raw("CONCAT_WS(' ',CPI.Emp_First_Name,CPI.Emp_Middle_Name, CPI.Emp_Last_Name) as name"))
            .from('candidate_personal_info'+' as CPI')
            .leftJoin('candidate_job'+' as CJ','CPI.Candidate_Id','CJ.Candidate_Id')
            .whereIn('CPI.Candidate_Id',signatureEmployeeIds)
            .then(candidateDetails => {
                if(candidateDetails && candidateDetails.length > 0){
                    return candidateDetails;
                }else{
                    return [];
                }
            });
        }
        //Check to employee details exists or not
        if (toEmployeeDetails.length > 0) {
            //Get the expiry time in UTC
            let urlExpiryTime = moment.utc().add(30,'days').format('YYYY-MM-DD HH:mm:ss');
            let failureCount = 0;
            for (let i in toEmployeeDetails){
                //Insert the authorizer details only when the email address exists.
                if(toEmployeeDetails[i].emailAddress){
                    let newAuthorizerDetails = {
                        Generated_Document_Id : generatedDocumentId,
                        Authorizer_Id: toEmployeeDetails[i].uniqueId,
                        Is_Candidate: (sendEmailToCandidate === 1) ? 1 : 0,
                        Expiry_Type: 'Days',
                        Expire_Time: urlExpiryTime,
                        Added_On: addedOn,
                        Added_By: addedBy
                    }
                    //Insert the authorizer details in the generated document signature details table
                    await 
                    organizationDbConnection('generated_document_signature_details')
                    .insert(newAuthorizerDetails)
                    .then(async() => {
                        let emailArgs = {
                            orgCode: orgCode,
                            encryptionKey: encryptionKey,
                            signatureUrlPath: signatureUrlPath,
                            firstAuthorizerEmployeeId: toEmployeeDetails[i].uniqueId,
                            generatedDocumentId: generatedDocumentId,
                            toEmployeeEmail: toEmployeeDetails[i].emailAddress,
                            sendEmailToCandidate:sendEmailToCandidate,
                            toEmployeeName: toEmployeeDetails[i].name,
                            candidateId:candidateId?candidateId:null,
                            templateName:Template_Name,
                            documentName: documentName,
                            sesTemplateName: awsSesTemplateName,
                            addedBy:addedBy
                        };
                        //Send an email to the authorizer
                        let emailResponse = await sendAuthorizerEmail(organizationDbConnection,emailArgs);
                        if(emailResponse === 'failure'){
                            console.log('Error in sending an email',newAuthorizerDetails);
                            failureCount++;
                        }
                    });
                }else{
                    console.log('Unable to send email as to employee email does not exist',toEmployeeDetails,'generatedDocumentId:',generatedDocumentId,'sendEmailToCandidate:',sendEmailToCandidate);
                    failureCount++;
                }
            }
            if(failureCount > 0){
                console.log('To employee/candidate details does not exist or error in sending an email.',toEmployeeDetails,'generatedDocumentId:',generatedDocumentId,'sendEmailToCandidate:',sendEmailToCandidate);
                throw 'CDG0120';
            }else{
                return 'success';
            }
        } else {
            console.log('To employee/candidate details does not exist.',toEmployeeDetails,'generatedDocumentId:',generatedDocumentId,'sendEmailToCandidate:',sendEmailToCandidate);
            throw 'CDG0119';
        }
    }catch(formSignatureInputsCatchError){
        console.log('Error in the formSignatureEmailInputsAndMail function main catch block.',formSignatureInputsCatchError);
        throw formSignatureInputsCatchError;
    }
}
async function getTemplateName(organizationDbConnection,documentId){
    try{
        //Require common library to access common function
        const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
        //Require table alias
        const { ehrTables } = commonLib.tableAlias;
    let templateNameArray=await organizationDbConnection(ehrTables.empGeneratedDocuments)
    .pluck('Template_Name')
    .where('Generated_Document_Id',documentId);
    if(templateNameArray && templateNameArray.length){
        return templateNameArray[0];
    }
}
catch(mainCatchErr) {
    throw mainCatchErr;
}

}
async function coverageIsCustomGroup (organizationDbConnection){
    let coverage= await organizationDbConnection('recruitment_settings').pluck('Coverage').limit(1);
    if(coverage && coverage.length && coverage[0].toLowerCase()==='custom group'){
        return true;
    }
    else{
        return false;
    }
}
async function getCustomGroupList (organizationDbConnection,loginEmpId,formId){
    let customGroupArray= await organizationDbConnection('custom_employee_group_employees as CEGE').pluck('CEGE.Group_Id').where('CEGE.Employee_Id',loginEmpId)
    .innerJoin('custom_group_associated_forms as CGAF','CGAF.Custom_Group_Id','CEGE.Group_Id' )
    .whereIn("CEGE.Type", ['AdditionalInclusion', 'Default'])
    .where('CGAF.Form_Id',formId);
    if(customGroupArray && customGroupArray.length){
        return customGroupArray;
    }
    else{
        return [];
    }
}

 // Get organization address based on location not in use now use the commonlib function
 const getOrgAddress =(organizationDbConnection, locationId) =>{
    return(
        organizationDbConnection('location')
        .select('location.Street1', 'location.Street2', 'location.Pincode', 'state.State_Name', 'country.Country_Name', 'city.City_Name')
        .from('location')
        .leftJoin('country', 'location.Country_Code','country.Country_Code')
        .leftJoin('state', 'location.State_Id','state.State_Id')
        .leftJoin('city', 'location.City_Id', 'city.City_Id')
        .where(qb => {                        
            if (locationId){
                qb.where('location.Location_Id', locationId);
            }else{
                qb.where('location.Location_Type','MainBranch');
            }
        })
        .then(orgDetails =>{                
            // return response
            return orgDetails;  
        })
    )
};

const getOrgDetails = (orgCode,organizationDbConnection) =>{
    return(
        organizationDbConnection('org_details')
        .select('Org_Name','Report_LogoPath','HR_Admin_Email_Address', 'Field_Force')
        .where('Org_Code',orgCode)
        .then(orgDetails =>{                
            // return response
            return orgDetails.length > 0 ? orgDetails[0] : {};
        })
    )
};
module.exports ={
    groupTemplateFieldsByTitle,
    getDocumentTemplateFields,
    sendAuthorizerEmail,
    formSignatureEmailInputsAndMail,
    groupTemplateFieldsByEntity,
    getTemplateName,
    coverageIsCustomGroup,
    getCustomGroupList,
    getOrgAddress,
    getOrgDetails

};
