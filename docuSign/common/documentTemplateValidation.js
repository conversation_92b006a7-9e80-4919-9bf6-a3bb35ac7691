//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

//Function to validate the add and update document template form inputs.
async function validateAddUpdateDocumentTemplateInputs(args, isEdit){
    try{
        let validationError = {};

        //If the isEdit is 1 then it is edit form
        if(isEdit === 1){
            //Validate the document template id exist or not during the edit
            if(args.documentTemplateId <= 0){
                throw '_EC0007';
            }
        }

        //Validate the document title
        if(args.title){
            if(!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.title)){
                validationError['IVE0222'] = commonLib.func.getError('', 'IVE0222').message2;
            }else{
                if(!commonLib.commonValidation.checkLength(args.title,3,150)){
                    validationError['IVE0222'] = commonLib.func.getError('', 'IVE0222').message3;
                }
            }
        }else{
            validationError['IVE0222'] = commonLib.func.getError('', 'IVE0222').message1;
        }

        //Validate the document content
        if(!args.templateContent?.trim()){
            validationError['IVE0223'] = commonLib.func.getError('', 'IVE0223').message1;
        }

        //Validate the registered business address
        if(!commonLib.commonValidation.booleanNumberValidation(args.registeredBusinessAddress)){
            validationError['IVE0224'] = commonLib.func.getError('', 'IVE0224').message;
        }

        return validationError;
    }catch(templateValidationCatchError){
        console.log('Error in the validateAddUpdateDocumentTemplateInputs() function in the main catch block.',templateValidationCatchError);
        throw templateValidationCatchError;
    }
}

async function validateAddUpdateDocumentInputs(args,isEdit){
    try{
        let validationError = {};
        //If the isEdit is 1 then it is edit form
        if(isEdit === 1){
            //Validate the document template id exist or not during the edit
            if(args.generatedDocumentId <= 0){
                console.log('Generated document id does not exist.')
                throw '_EC0007';
            }
        }

        //Validate employee id and candidate
        if(args.employeeId < 0 || args.candidateId < 0){
            validationError['IVE0228'] = commonLib.func.getError('', 'IVE0228').message;
        }

        //Validate the template name
        if(!args.templateName){
            validationError['IVE0222'] = commonLib.func.getError('', 'IVE0222').message1;
        }

        //Validate the document name
        if(args.documentName){
            if(!commonLib.commonValidation.alphaNumSpaceSymbolsValidation(args.documentName)){
                validationError['IVE0229'] = commonLib.func.getError('', 'IVE0229').message2;
            }else{
                if(!commonLib.commonValidation.checkLength(args.documentName,3,150)){
                    validationError['IVE0229'] = commonLib.func.getError('', 'IVE0229').message3;
                }
            }
        }else{
            validationError['IVE0229'] = commonLib.func.getError('', 'IVE0229').message1;
        }

        //Validate the registered business address
        if(!['Main Branch','Employee Location'].includes(args.registeredBusinessAddress)){
            validationError['IVE0224'] = commonLib.func.getError('', 'IVE0224').message;
        }

        //Validate the document content
        if(!args.documentContent?.trim()){
            validationError['IVE0223'] = commonLib.func.getError('', 'IVE0223').message1;
        }

        //Validate the status
        if(!['Draft','In Review','Completed'].includes(args.status)){
            console.log('Invalid status in the validateAddUpdateDocumentInputs() function.',args.status);
            throw '_EC0007';
        }

        return validationError;
    }catch(catchError){
        console.log('Error in the validateAddUpdateDocumentInputs() function in the main catch block.',catchError);
        throw catchError;
    }
}

module.exports={
    validateAddUpdateDocumentTemplateInputs,
    validateAddUpdateDocumentInputs
};