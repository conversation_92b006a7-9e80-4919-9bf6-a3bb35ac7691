const formName = {
    docuSign: 'DocuSign',
    accreditation:'Accreditation',
    serviceProviderAdmin: 'Service Provider Admin',
};

const formIds = {
    accreditation:232,
    docuSign:134,
    jobPost: 15,
    serviceProviderAdmin: 219,
    reportDefinition: 380
};

const systemLogs = {
    roleAdd: 'Add',
    roleUpdate: 'Update',
    roleDelete: 'Delete',
    offerLetter: 'Offer Letter',
    docuSign: 'DocuSign',
};

const urlEncryption = {
    encryptionKey : 'pD!m*LsZQmT4'
}

const defaultValues = {
    authorizedSignatoryComponentName:'Signing Authority',
    adminFormIdArray:[22,148,149,222,218],//admin,employee admin, payroll admin, roster admin, benefits admin
    resignationStatus:['Approved', 'Applied', 'Incomplete'],
    signatureUrlPath: 'docusign',
    interviewRoundStatus: ['Pass','Cancelled','Closed'],
    s3DefaultEncryption:"AES256"
};

const awsSesTemplates = {
    sendDocumentLinkToSign: 'SendDocumentLinkToSign',
    jobOfferRejectionManager:'JobOfferRejectionManager',
    offerLetterAccepted:'OfferLetterAccepted',
    jobOfferRejectionInitiator:'jobOfferRejectionInitiator',
    offerLetterTemplate:'OfferLetterTemplate'
};
module.exports={
    formName,
    systemLogs,
    defaultValues,
    urlEncryption,
    awsSesTemplates,
    formIds
};