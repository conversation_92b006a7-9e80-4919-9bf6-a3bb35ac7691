//Require constants
const {defaultValues} = require('./appconstants');
const puppeteer = require('puppeteer-core');
const chromium = require('@sparticuz/chromium');

//Upload the document in the s3
async function uploadDocumentInS3(documentFileName,pdfDocumentContent, bucketName = process.env.encryptedDocBucket){ 
    try{
        return new Promise(async function (resolve, reject) {
            pdfDocumentContent = pdfDocumentContent.toString('utf8');
            const AWS = require('aws-sdk');
            const lambda = new AWS.Lambda({ region: process.env.region });
            const params = {
                FunctionName: "PDFCREATION-"+process.env.stageName+"-createPDF", // Replace with the name of the target Lambda function
                InvocationType: 'RequestResponse', // Use 'Event' for asynchronous invocation, 'RequestResponse' for synchronous
                Payload: JSON.stringify({ 'inputData': pdfDocumentContent, 'pathOfFileToEncrypt': '' }) // Payload to send to the target function
            };
            try {
                let response = await lambda.invoke(params).promise();

                if(response && response.Payload && JSON.parse(response.Payload) ) {
                    content = JSON.parse(response.Payload);
                }else{
                    console.log('Inside uploadDocumentInS3 function create-pdf lambda response,',response);
                }
                // convert string to Buffer
                const pdfContentBuffer = Buffer.from(content, 'utf-8');
                // Create object for s3 bucket
                const s3 = new AWS.S3({ region: process.env.region });
                // Params to upload
                let s3Params = {
                    Body: pdfContentBuffer,
                    Bucket: bucketName ? bucketName : process.env.encryptedDocBucket,
                    Key: documentFileName,
                    ContentType: 'application/pdf',
                    ServerSideEncryption:defaultValues.s3DefaultEncryption
                };
                console.log('s3Params',s3Params)
                await s3.upload(s3Params).promise();
                resolve({
                    result: 'success'
                });
            } catch (invocationError) {
                console.error('Error in the uploadDocumentInS3 function - create pdf invocation main catch block', invocationError);
                resolve({
                    result: 'failure'
                });
            }
        })
        .catch(function(generatePdfError) {
            console.log('Error while generating file as pdf in uploadDocument function in .catch block.', generatePdfError);
            resolve({
                result: 'failure'
            });
        });
    }catch(uploadDocumentCatchError){
        console.log('Error while uploading the file in s3 in uploadDocument function in main catch block.', uploadDocumentCatchError);
        return 'failure';
    }
}

//Upload the text document
async function uploadTextDocumentInS3(s3,fileName,documentContent){
    try{
        return new Promise(async function (resolve, reject) {
            let newHtml = documentContent;
            //Params to upload
            let s3Params = {
                Body: newHtml,
                Bucket: process.env.encryptedDocBucket,
                Key: fileName,
                ContentType: 'application/txt',
                ServerSideEncryption:defaultValues.s3DefaultEncryption
            };
            await s3.upload(s3Params).promise();
            resolve({
                result: 'success'
            });
        })
    }catch(htmlCatchError){
        console.log('Error in the uploadHtmlDocumentInS3 function main catch block.',htmlCatchError);
        throw htmlCatchError;
    }
}

async function copyS3Document(fileName,documentFileName){
    console.log('Inside copyS3Document function.',fileName,documentFileName);

    try{
        return new Promise(async function (resolve, reject) {

            try {
                const AWS = require('aws-sdk');
                const s3 = new AWS.S3({ region: process.env.region });
                let copyParams = {
                    CopySource: encodeURIComponent(process.env.encryptedDocBucket+"/"+documentFileName), 
                    Bucket: process.env.documentsBucket,
                    Key: fileName,
                    ServerSideEncryption: defaultValues.s3DefaultEncryption
                };
                console.log('copyParams',copyParams);

                // Move source to designation path
                await s3.copyObject(copyParams).promise();
                resolve({
                    result: 'success'
                });
            }catch(err){
                console.log("My err:",err);
                resolve({
                    result: 'failure'
                });
            }

        })
    }catch(err){
        console.log('Error while copying the file in s3 in copyS3Document function in main catch block.', err);
        return 'failure';
    }
}


async function checkFileIsExsit(bucketName, filePath){
    try{
        const AWS = require('aws-sdk');
        const s3 = new AWS.S3({ region: process.env.region });
        await s3.headObject({ Bucket: bucketName, Key: filePath }).promise();
        return filePath;
    }catch(err){
        console.error(err)
        return '';
    }
}

//Helper function to detect MIME type from file extension
function getMimeTypeFromUrl(url) {
    const extension = url.split('.').pop().toLowerCase().split('?')[0];
    const mimeTypes = {
        'png': 'image/png',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'gif': 'image/gif',
        'svg': 'image/svg+xml',
        'webp': 'image/webp',
        'bmp': 'image/bmp',
        'ico': 'image/x-icon'
    };
    return mimeTypes[extension] || 'image/png';
}

//Helper function to fetch image from S3 using AWS SDK
async function fetchImageFromS3(url) {
    try {
        // Check if it's an S3 URL - support multiple S3 URL formats
        // Format 1: https://bucket.s3.region.amazonaws.com/key
        // Format 2: https://s3.region.amazonaws.com/bucket/key
        // Also support CloudFront URLs: https://documents.hrapp.co.in/path/to/file.png
        let bucketName, key;

        const format1Match = url.match(/https?:\/\/([^.]+)\.s3[.-]([^.]+)\.amazonaws\.com\/(.+)/i);
        const format2Match = url.match(/https?:\/\/s3[.-]([^.]+)\.amazonaws\.com\/([^/]+)\/(.+)/i);
        const cloudFrontMatch = url.match(/https?:\/\/documents\.hrapp\.co\.in\/(.+)/i);

        if (format1Match) {
            bucketName = format1Match[1];
            key = format1Match[3];
        } else if (format2Match) {
            bucketName = format2Match[2];
            key = format2Match[3];
        } else if (cloudFrontMatch) {
            // For CloudFront URLs, use the documents bucket
            bucketName = process.env.documentsBucket || 'hrapp-documents';
            key = cloudFrontMatch[1];
        } else {
            return null; // Not an S3 URL
        }

        // Remove query parameters from the key (e.g., ?Expires=...&Key-Pair-Id=...&Signature=...)
        key = key.split('?')[0];

        // Decode the key
        key = decodeURIComponent(key);

        console.log(`Fetching S3 image from bucket: ${bucketName}, key: ${key}`);

        const AWS = require('aws-sdk');
        const s3 = new AWS.S3({ region: process.env.region || 'ap-south-1' });

        const params = {
            Bucket: bucketName,
            Key: key
        };

        const data = await s3.getObject(params).promise();
        const base64 = data.Body.toString('base64');

        // Use ContentType from S3, or detect from URL if invalid
        let mimeType = data.ContentType;
        if (!mimeType || mimeType === 'application/x-www-form-urlencoded' || mimeType === 'application/octet-stream') {
            mimeType = getMimeTypeFromUrl(url);
            console.log(`S3 ContentType was invalid (${data.ContentType}), detected from extension: ${mimeType}`);
        }

        console.log(`S3 image fetched successfully. Size: ${data.Body.length} bytes, Type: ${mimeType}`);
        return `data:${mimeType};base64,${base64}`;
    } catch (error) {
        console.error('Error fetching image from S3:', error.message);
        return null;
    }
}

//Helper function to convert image URLs to base64
async function convertImageToBase64(url) {
    try {
        console.log('Converting image to base64:', url);

        // Try S3 SDK first if it's an S3 URL
        const s3Result = await fetchImageFromS3(url);
        if (s3Result) {
            return s3Result;
        }

        // Fall back to HTTP/HTTPS download
        const https = require('https');
        const http = require('http');

        return new Promise((resolve, reject) => {
            const protocol = url.startsWith('https') ? https : http;

            const request = protocol.get(url, (response) => {
                // Handle redirects
                if (response.statusCode === 301 || response.statusCode === 302) {
                    console.log('Image redirect detected, following to:', response.headers.location);
                    convertImageToBase64(response.headers.location).then(resolve).catch(() => resolve(url));
                    return;
                }

                if (response.statusCode !== 200) {
                    console.error('Failed to fetch image, status code:', response.statusCode);
                    resolve(url);
                    return;
                }

                const chunks = [];

                response.on('data', (chunk) => chunks.push(chunk));

                response.on('end', () => {
                    try {
                        const buffer = Buffer.concat(chunks);
                        const base64 = buffer.toString('base64');

                        // Get MIME type from response header or detect from URL
                        let mimeType = response.headers['content-type'];
                        if (!mimeType || mimeType === 'application/x-www-form-urlencoded' || mimeType === 'application/octet-stream') {
                            mimeType = getMimeTypeFromUrl(url);
                            console.log(`HTTP ContentType was invalid (${response.headers['content-type']}), detected from extension: ${mimeType}`);
                        }

                        const dataUri = `data:${mimeType};base64,${base64}`;
                        console.log(`Image converted successfully. Size: ${buffer.length} bytes, Type: ${mimeType}`);
                        resolve(dataUri);
                    } catch (err) {
                        console.error('Error converting buffer to base64:', err);
                        resolve(url);
                    }
                });

                response.on('error', (err) => {
                    console.error('Error in response stream:', err);
                    resolve(url);
                });
            });

            request.on('error', (err) => {
                console.error('Error fetching image:', err);
                resolve(url);
            });

            // Set timeout for image download
            request.setTimeout(10000, () => {
                console.error('Image download timeout for:', url);
                request.destroy();
                resolve(url);
            });
        });
    } catch (error) {
        console.error('Error in convertImageToBase64:', error);
        return url; // Fallback to original URL
    }
}

//Helper function to convert all images in HTML to base64
async function convertHtmlImagesToBase64(html) {
    if (!html) return html;

    const imgRegex = /<img[^>]+src="([^">]+)"/gi;
    let match;
    const imageUrls = [];

    // Extract all image URLs
    while ((match = imgRegex.exec(html)) !== null) {
        imageUrls.push(match[1]);
    }

    console.log(`Found ${imageUrls.length} images to convert:`, imageUrls);

    // Convert each image to base64
    let updatedHtml = html;
    for (const url of imageUrls) {
        if (url.startsWith('http://') || url.startsWith('https://')) {
            console.log('Converting image URL:', url);
            const base64Data = await convertImageToBase64(url);
            if (base64Data !== url) {
                console.log('Successfully converted image, replacing in HTML');
                // Use a function to escape special regex characters in the URL for replacement
                const escapedUrl = url.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                updatedHtml = updatedHtml.replace(new RegExp(escapedUrl, 'g'), base64Data);
            } else {
                console.log('Image conversion failed, keeping original URL');
            }
        } else {
            console.log('Skipping non-HTTP image:', url);
        }
    }

    return updatedHtml;
}

//Generate PDF using Puppeteer with header and footer support
async function generatePDFWithPuppeteer(documentContent, documentHeader = '', documentFooter = '', pageScope = 'All Pages'){
    console.log('Inside generatePDFWithPuppeteer function.');
    let browser = null;
    try{
        // Convert images to base64 for reliable rendering in headers/footers
        documentHeader = await convertHtmlImagesToBase64(documentHeader);
        documentFooter = await convertHtmlImagesToBase64(documentFooter);
        documentContent = await convertHtmlImagesToBase64(documentContent);

        // Handle pageScope logic
        let finalDocumentContent = documentContent;
        let finalDocumentHeader = documentHeader;
        let finalDocumentFooter = documentFooter;

        if (pageScope === 'First Page Only') {
            // For "First Page Only", append header content before main content and footer content after main content
            // This way header appears only on first page and footer only on last page
            console.log('Using "First Page Only" pageScope - appending header and footer to content');

            // Combine header + content + footer into a single content block
            finalDocumentContent = '';
            if (documentHeader) {
                finalDocumentContent += documentHeader;
            }
            finalDocumentContent += documentContent;
            if (documentFooter) {
                finalDocumentContent += documentFooter;
            }

            // Clear header and footer since they're now part of the content
            finalDocumentHeader = '';
            finalDocumentFooter = '';
        }

        // Create HTML template with proper structure for headers and footers
        const htmlTemplate = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {
            margin: 0;
            padding: 0;
        }
        .content {
            width: 100%;
        }
        table {
            border-collapse: collapse;
            width: 100%;
        }
        table, th, td {
            border: 1px solid black;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        .table {
            margin: 0;
        }
        p {
            font-family: Helvetica, Arial, Tahoma, Verdana, Sans-Serif;
            font-size: 12pt;
            line-height: 1.5;
            overflow-wrap: break-word;
            margin-top: 0px !important;
            margin-bottom: 0px !important;
            }
        .page-break {
            page-break-after: always;
            break-after: page;
        }
    </style>
</head>
<body>
    <div class="content">
        ${finalDocumentContent}
    </div>
</body>
</html>
        `;

        // Prepare header and footer HTML templates with proper styling
        // Note: Puppeteer header/footer templates have limited CSS support
        // Using inline styles and ensuring images are properly sized
        const headerTemplate = finalDocumentHeader ? `
            <div style="width: 100%; font-size: 10pt; padding: 10px 50px; -webkit-print-color-adjust: exact; box-sizing: border-box;">
                <style>
                    * { margin: 0; padding: 0; box-sizing: border-box; font-size: 10pt !important; }
                    .header-content { width: 100%; font-size: 12pt !important; }
                    .header-content table { width: 100%; border-collapse: collapse; border: none !important; }
                    .header-content table td { border: none !important; padding: 5px; vertical-align: middle; }
                    .header-content img { display: inline-block; vertical-align: middle; }
                    .header-content h1 { margin: 2px 0; font-size: 14pt !important; }
                    .header-content h2 { margin: 2px 0; font-size: 13pt !important; }
                    .header-content h3 { margin: 2px 0; font-size: 12pt !important; }
                    .header-content h4 { margin: 2px 0; font-size: 11pt !important; }
                    .header-content p { margin: 2px 0; font-size: 10pt !important; line-height: 1.4 !important; }
                    .header-content .font-small { font-size: 8pt !important; }
                </style>
                <div class="header-content">
                    ${finalDocumentHeader}
                </div>
            </div>
        ` : '<div style="height: 0; margin: 0; padding: 0;"></div>';

        const footerTemplate = finalDocumentFooter ? `
            <div style="width: 100%; font-size: 10pt; padding: 10px 50px; -webkit-print-color-adjust: exact; box-sizing: border-box;">
                <style>
                    * { margin: 0; padding: 0; box-sizing: border-box; font-size: 10pt !important; }
                    .footer-content { width: 100%; font-size: 12pt !important; }
                    .footer-content table { width: 100%; border-collapse: collapse; border: none !important; }
                    .footer-content table td { border: none !important; padding: 5px; vertical-align: middle; }
                    .footer-content img { display: inline-block; vertical-align: middle; max-width: 100%; height: auto; }
                    .footer-content figure { margin: 0; padding: 0; display: inline-block; }
                    .footer-content figure img { display: block; max-width: 100%; height: auto; }
                    .footer-content h1 { margin: 2px 0; font-size: 14pt !important; }
                    .footer-content h2 { margin: 2px 0; font-size: 13pt !important; }
                    .footer-content h3 { margin: 2px 0; font-size: 12pt !important; }
                    .footer-content h4 { margin: 2px 0; font-size: 11pt !important; }
                    .footer-content p { margin: 2px 0; font-size: 10pt !important; line-height: 1.4 !important; }
                    .footer-content .font-small { font-size: 8pt !important; }
                </style>
                <div class="footer-content">
                    ${finalDocumentFooter}
                </div>
            </div>
        ` : '<div></div>';

        // Helper function to estimate header/footer height more accurately
        const estimateContentHeight = (content) => {
            if (!content) return 0;

            // Count various HTML elements
            const lineBreaks = (content.match(/<br\s*\/?>/gi) || []).length;
            const pTags = (content.match(/<p[^>]*>/gi) || []).length;
            const h1Tags = (content.match(/<h1[^>]*>/gi) || []).length;
            const h2Tags = (content.match(/<h2[^>]*>/gi) || []).length;
            const h3Tags = (content.match(/<h3[^>]*>/gi) || []).length;
            const h4Tags = (content.match(/<h4[^>]*>/gi) || []).length;
            const tableTags = (content.match(/<table[^>]*>/gi) || []).length;
            const trTags = (content.match(/<tr[^>]*>/gi) || []).length;

            // Extract image height from style attribute or use default
            let totalImageHeight = 0;
            const imgMatches = content.matchAll(/<img[^>]*>/gi);
            for (const imgMatch of imgMatches) {
                const imgTag = imgMatch[0];
                // Check for height in style attribute or height attribute
                const heightMatch = imgTag.match(/height:\s*(\d+)px/i) ||
                                   imgTag.match(/height="(\d+)"/i) ||
                                   imgTag.match(/height='(\d+)'/i);
                if (heightMatch) {
                    totalImageHeight += parseInt(heightMatch[1]);
                } else {
                    // Default image height
                    totalImageHeight += 50;
                }
            }

            let height = 0;

            // For table-based layouts (like your header)
            if (tableTags > 0 && trTags > 0) {
                // Base row height
                let rowHeight = 20; // Base padding

                // Add height for content in cells
                if (h4Tags > 0) rowHeight += 18; // h4 is ~18px
                if (pTags > 0) rowHeight += pTags * 16; // Each p is ~16px

                // Total table height
                height = trTags * rowHeight;

                // Add image height (images are usually in table cells)
                height = Math.max(height, totalImageHeight + 20); // 20px for padding
            } else {
                // Non-table layout - calculate line by line
                let lines = 0;
                lines += lineBreaks;
                lines += pTags * 1.2;
                lines += h1Tags * 2.5;
                lines += h2Tags * 2;
                lines += h3Tags * 1.7;
                lines += h4Tags * 1.5;

                height = lines * 18; // 18px per line
                height += totalImageHeight;
            }

            // Add padding (15px top + 15px bottom) and border (1px)
            height += 31;

            // Add minimal safety margin (10% for rendering variations)
            height = Math.ceil(height * 1.1);

            console.log(`Content height calculation: base=${height-Math.ceil(height*0.1)}, images=${totalImageHeight}, final=${height}`);

            // Minimum height of 70px, maximum of 350px
            return Math.max(70, Math.min(height, 350));
        };

        // Calculate dynamic margins based on content
        const headerHeight = finalDocumentHeader ? estimateContentHeight(finalDocumentHeader) : 0;
        const footerHeight = finalDocumentFooter ? estimateContentHeight(finalDocumentFooter) + 50 : 0; // +50 for larger fonts and additional spacing

        console.log(`Estimated header height: ${headerHeight}px, footer height: ${footerHeight}px`);

        // Launch Puppeteer browser
        // Check if running in AWS Lambda environment
        const isLambda = !!process.env.LAMBDA_TASK_ROOT;

        let browserOptions = {
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--single-process',
                '--no-zygote'
            ]
        };

        if (isLambda) {
            // AWS Lambda environment - use @sparticuz/chromium
            browserOptions.executablePath = await chromium.executablePath();
            browserOptions.args = chromium.args;
        } else {
            // Local environment - try to find Chrome/Chromium
            const possiblePaths = [
                '/usr/bin/google-chrome',
                '/usr/bin/chromium-browser',
                '/usr/bin/chromium',
                '/snap/bin/chromium',
                'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
                'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
                '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
            ];

            const fs = require('fs');
            for (const path of possiblePaths) {
                if (fs.existsSync(path)) {
                    browserOptions.executablePath = path;
                    break;
                }
            }
        }

        console.log('Launching browser with options:', JSON.stringify(browserOptions));
        browser = await puppeteer.launch(browserOptions);

        const page = await browser.newPage();

        // Set a longer timeout for image loading
        page.setDefaultNavigationTimeout(60000); // 60 seconds
        page.setDefaultTimeout(60000);

        // Set content and wait for it to load
        await page.setContent(htmlTemplate, { waitUntil: 'networkidle0' });

        // Wait for all images to load in the main content
        await page.evaluate(() => {
            return Promise.all(
                Array.from(document.images)
                    .filter(img => !img.complete)
                    .map(img => new Promise((resolve) => {
                        img.onload = img.onerror = resolve;
                    }))
            );
        });

        // Add a small delay to ensure images are fully rendered
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Generate PDF with header and footer using dynamic margins
        const pdfBuffer = await page.pdf({
            format: 'A4',
            printBackground: true,
            displayHeaderFooter: true,
            headerTemplate: headerTemplate,
            footerTemplate: footerTemplate,
            preferCSSPageSize: false,
            margin: {
                top: finalDocumentHeader ? `${headerHeight}px` : '50px',
                bottom: (finalDocumentFooter || footerHeight > 0) ? `${Math.max(footerHeight, 80)}px` : '80px',
                left: '50px',
                right: '50px'
            }
        });

        await browser.close();
        browser = null;

        console.log('PDF generated successfully with Puppeteer.');
        return pdfBuffer;

    }catch(puppeteerError){
        console.log('Error in generatePDFWithPuppeteer function:', puppeteerError);
        if (browser) {
            await browser.close();
        }
        throw puppeteerError;
    }
}

//Upload the PDF document generated by Puppeteer to S3
async function uploadPuppeteerPDFToS3(documentFileName, documentContent, documentHeader = '', documentFooter = '', bucketName = process.env.encryptedDocBucket, pageScope = 'All Pages'){
    console.log('Inside uploadPuppeteerPDFToS3 function.');
    try{
        return new Promise(async function (resolve, reject) {
            try {
                // Generate PDF using Puppeteer
                const pdfBuffer = await generatePDFWithPuppeteer(documentContent, documentHeader, documentFooter, pageScope);

                // Upload to S3
                const AWS = require('aws-sdk');
                const s3 = new AWS.S3({ region: process.env.region });

                let s3Params = {
                    Body: pdfBuffer,
                    Bucket: bucketName ? bucketName : process.env.encryptedDocBucket,
                    Key: documentFileName,
                    ContentType: 'application/pdf',
                    ServerSideEncryption: defaultValues.s3DefaultEncryption
                };

                console.log('Uploading PDF to S3 with params:', {
                    Bucket: s3Params.Bucket,
                    Key: s3Params.Key,
                    ContentType: s3Params.ContentType
                });

                await s3.upload(s3Params).promise();

                console.log('PDF uploaded successfully to S3.');
                resolve({
                    result: 'success'
                });
            } catch (uploadError) {
                console.error('Error in uploadPuppeteerPDFToS3 function:', uploadError);
                resolve({
                    result: 'failure',
                    error: uploadError.message
                });
            }
        })
        .catch(function(catchError) {
            console.log('Error in uploadPuppeteerPDFToS3 function .catch block:', catchError);
            return {
                result: 'failure',
                error: catchError.message
            };
        });
    }catch(mainCatchError){
        console.log('Error in uploadPuppeteerPDFToS3 function main catch block:', mainCatchError);
        return {
            result: 'failure',
            error: mainCatchError.message
        };
    }
}

module.exports ={
    uploadDocumentInS3,
    uploadTextDocumentInS3,
    copyS3Document,
    checkFileIsExsit,
    generatePDFWithPuppeteer,
    uploadPuppeteerPDFToS3
};