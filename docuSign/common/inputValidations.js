//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { validateWithRules } = require('@cksiva09/validationlib/src/validator');


async function validateCustomComponentInputs(args, fieldValidations) {
    try{
        let validationError = {};
        for (const field in fieldValidations) {
            if (!args.hasOwnProperty(field)) continue; // Skip if field doesn't exist

            let ruleField = field;
            let errorCode = fieldValidations[field];

            // If structured metadata is provided
            if (typeof fieldValidations[field] === 'object') {
                ruleField = fieldValidations[field].ruleField;
                errorCode = fieldValidations[field].errorCode;
            }

            const validation = validateWithRules(args[field], ruleField);
            if (validation !== 'Validation not found' && (!validation.validation || !validation.length)) {
                validationError[errorCode] = validation.length ? commonLib.func.getError('', errorCode).message : commonLib.func.getError('', errorCode).message1;
            }
        }
        return validationError;
    }
    catch (err) {
        console.log('Error in the validateCustomComponentInputs function in the main catch block.', err);
        throw err;
    }
}

module.exports={
    validateCustomComponentInputs,
};