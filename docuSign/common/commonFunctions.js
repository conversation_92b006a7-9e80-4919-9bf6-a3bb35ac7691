const { CommonLib } = require('@cksiva09/hrapp-corelib');
const {coverageIsCustomGroup, getCustomGroupList, getOrgDetails}=require('./documentGeneratorCommonFunctions');
const { formIds } = require('./appconstants');
const { ehrTables } = require('./tableAlias');

async function fetchJobRoleDetails(jobInfoDetails, organizationDbConnection) {
    try {
        // Get all unique job role IDs
        const jobRoleIds = Array.from(
            new Set(
                jobInfoDetails.flatMap(
                    ({ Job_Role_Ids }) => JSON.parse(Job_Role_Ids || '[]')
                )
            )
        );

        // Fetch all job roles
        const jobRoles = jobRoleIds.length ? await organizationDbConnection('job_roles'+ " as JR")
            .select(
                organizationDbConnection.raw(`
                    CASE WHEN JR.Job_Role_Code IS NOT NULL AND TRIM(JR.Job_Role_Code) != '' 
                    THEN CONCAT(JR.Job_Role_Code, ' - ', <PERSON>.Job_Role) 
                    ELSE JR.Job_Role END AS Job_Role_Name
                `),
                'JR.Job_Role_Id'
            )
            .whereIn('JR.Job_Role_Id', jobRoleIds) : [];

        // Create lookup object
        const jobRolesMap = Object.fromEntries(
            jobRoles.map(role => [role.Job_Role_Id, role])
        );

        // Map results with job roles
        return jobInfoDetails.map(record => ({
            ...record,
            Job_Role_Ids: JSON.parse(record.Job_Role_Ids || '[]'),
            Job_Role_Details: JSON.parse(record.Job_Role_Ids || '[]')
                .map(id => jobRolesMap[id])
                .filter(Boolean)
        }));
        
    } catch (e) {
        console.log('Error in fetchJobRoleDetails:', e);
        throw e;
    }
}

async function getJobPostBasedOnRole(organizationDbConnection, checkRights, loginEmpId, orgCode) {
    try {
        let employeeRole = (checkRights?.Employee_Role === 'admin' ? 'admin' :
            checkRights?.Is_Recruiter && checkRights?.Is_Recruiter?.toLowerCase() === 'yes' ? 'recruiter' :
            checkRights?.Is_Manager === 1 ? 'manager' : 'employee');
      
        const [isCustomGroup, centralisedRecuitment, orgDetails] = await Promise.all([
            coverageIsCustomGroup(organizationDbConnection),
            CommonLib.func.getCentralisedRecuitment(organizationDbConnection),
            getOrgDetails(orgCode, organizationDbConnection)
        ]);
        
        const fieldForce = orgDetails && orgDetails.Field_Force ? orgDetails.Field_Force : 0;
        if (employeeRole !== 'admin' && fieldForce && centralisedRecuitment.toLowerCase() === 'no') {
            let spCheckRights = await CommonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmpId, '', '', 'UI', false, formIds.serviceProviderAdmin)
            employeeRole = spCheckRights && spCheckRights.Role_Update === 1 ? 'sp-admin' : employeeRole;
        }
      
        // Cache repeated operations for better performance
        const normalizedRole = employeeRole.toLowerCase();
        const isCentralisedRecruitmentDisabled = centralisedRecuitment.toLowerCase() === 'no';

        let jobPostIdList = [];

        // Use object-based strategy pattern for cleaner conditional logic
        const roleHandlers = {
            admin: async () => {
                return await organizationDbConnection(ehrTables.jobPost).pluck('Job_Post_Id');
            },

            recruiter: async () => {
                if (isCustomGroup) {
                    // Handle custom group recruiter
                    let customGroupJobs = [];
                    const customGroupList = await getCustomGroupList(organizationDbConnection, loginEmpId, formIds.jobPost);

                    if (customGroupList?.length) {
                        customGroupJobs = await organizationDbConnection(ehrTables.jobPost)
                            .pluck('Job_Post_Id')
                            .whereIn('Custom_Group_Id', customGroupList);
                    }

                    const employeeIds = [loginEmpId]; // Add loginEmpId to the employeeIds array for hiring team jobs.
                    const hiringTeamJobs = await getHiringTeamJobs(organizationDbConnection, employeeIds);

                    // Combine and remove duplicates
                    const allJobs = [...customGroupJobs, ...hiringTeamJobs.flat()];
                    return [...new Set(allJobs)];
                }

                if (fieldForce && isCentralisedRecruitmentDisabled) {
                    return await organizationDbConnection(ehrTables.jobPost + ' as JP')
                        .pluck('JP.Job_Post_Id')
                        .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Service_Provider_Id', 'JP.Service_Provider_Id')
                        .where('EJ.Employee_Id', loginEmpId)
                        .where('JP.Added_By', loginEmpId);
                }

                // Default: all job posts
                return await organizationDbConnection(ehrTables.jobPost).pluck('Job_Post_Id');
            },

            'sp-admin': async () => {
                if (!fieldForce) return [];

                return await organizationDbConnection(ehrTables.jobPost + ' as JP')
                    .pluck('JP.Job_Post_Id')
                    .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Service_Provider_Id', 'JP.Service_Provider_Id')
                    .where(function () {
                        this.where('EJ.Employee_Id', loginEmpId)
                            .orWhere('JP.Added_By', loginEmpId);
                    });
            },

            manager: async () => {
                const managerHierarchy = await CommonLib.func.getManagerHierarchy(organizationDbConnection, loginEmpId);
                const employeeIds = [loginEmpId, ...managerHierarchy];

                const hiringTeamJobs = await getHiringTeamJobs(organizationDbConnection, employeeIds);
                return hiringTeamJobs.flat();
            },

            employee: async () => {
                return await organizationDbConnection(ehrTables.jobPostHiringManagers)
                    .pluck('Job_Post_Id')
                    .whereIn('Hiring_Manager_Id', [loginEmpId]);
            }
        };

        // Execute the appropriate handler or return empty array for unknown roles
        const handler = roleHandlers[normalizedRole];
        jobPostIdList = handler ? await handler() : [];

        return jobPostIdList;

    }catch (error) {
        console.error('Error in getting the getJobPostBasedOnRole catch block:', error);
        throw error;
    } 
}

async function getHiringTeamJobs(organizationDbConnection, employeIds) {
    try {
        // Get hiring team jobs in parallel
        const hiringTeamJobs = await Promise.all([
            organizationDbConnection(ehrTables.jobPost).pluck('Job_Post_Id').whereIn('Added_By', employeIds),
            organizationDbConnection(ehrTables.jobPostRecruiters).pluck('Job_Post_Id').whereIn('Recruiter_Id', employeIds),
            organizationDbConnection(ehrTables.jobPostHiringManagers).pluck('Job_Post_Id').whereIn('Hiring_Manager_Id', employeIds)
        ]);

        return hiringTeamJobs;

    } catch (error) {
        console.error('Error in getting the getHiringTeamJobs catch block:', error);
        throw error;
    }
}

async function sendDynamicTemplateEmail(config) {
    try {
      const { organizationDbConnection, templateConfig, context, mailConfiguration,
        additionalPlaceholderData, employeeId } = config;
  
      // Try to get dynamic template first
      const defaultTemplate = await CommonLib.func.getDefaultTemplate(templateConfig, organizationDbConnection);
      let sendEmailResponse = false;
  
      if (defaultTemplate) {
        // Use dynamic template approach
        try {
          // Get template content and email configuration
          const { emailResult, ses, event } = await CommonLib.func.listEmailTemplatePlaceHolderValues(
            { templateId: defaultTemplate.Template_Id, employeeId: employeeId},
            organizationDbConnection,
            context,
            mailConfiguration,
            [],
            additionalPlaceholderData
          );
  
          const { emails = {}, Template_Content, Subject_Content } = emailResult;
  
          // Prepare all recipients - ensure target email is always included
          const allToEmails = emails.toEmails;
          const allCcEmails = emails?.ccEmails || [];
          const allBccEmails = emails?.bccEmails || [];
          const allAdditionalEmails = emails?.additionalEmails || [];
  
          // Send email using nodemailer with batch processing
          sendEmailResponse = await sendEmailWithNodemailer({
            toEmails: allToEmails,
            ccEmails: allCcEmails,
            bccEmails: allBccEmails,
            additionalEmails: allAdditionalEmails,
            subject: Subject_Content,
            htmlContent: Template_Content,
            fromEmail: event.sourceEmail,
            replyToEmails: event?.replyToEmails || [],
            fromEmailSenderName: defaultTemplate?.Sender_Name,
            ses: ses
          });
  
        } catch (templateError) {
          console.error('Error using dynamic Email template, falling back to AWS SES:', templateError);
          sendEmailResponse = false;
        }
      } 
      return sendEmailResponse;
  
    } catch (error) {
      console.error('Error in sendDynamicTemplateEmail main catch block:', error);
      return false;
    }
  }
  
  
  async function sendEmailWithNodemailer(emailConfig) {
    console.log("Inside sendEmailWithNodemailer function");
    try {
      const { toEmails = [], ccEmails = [], bccEmails = [], additionalEmails = [],
        subject, htmlContent, fromEmail, replyToEmails, fromEmailSenderName, ses } = emailConfig;
  
      // Create AWS SES transporter
      const AWS = require('aws-sdk');
      const sesTemplate = ses ? ses : new AWS.SES({ region: process.env.sesTemplatesRegion });
  
      const nodemailer = require("nodemailer");
      const transporter = nodemailer.createTransport({ SES: sesTemplate });
  
      // Combine all recipients for batch processing
      const allRecipients = [
        ...toEmails,
        ...ccEmails,
        ...bccEmails,
        ...additionalEmails
      ].filter((email, index, self) => email && self.indexOf(email) === index); // Remove duplicates and empty values
  
      if (allRecipients.length === 0) {
        console.error('Inside sendEmailWithNodemailer no recipients found for email');
        return false;
      }
  
      // Process emails in batches of 50 (AWS SES limit)
      const batchSize = 50;
      let successCount = 0;
  
      for (let i = 0; i < allRecipients.length; i += batchSize) {
        const batch = {
          to: toEmails.slice(i, Math.min(i + batchSize, toEmails.length)),
          cc: ccEmails.slice(i, Math.min(i + batchSize, ccEmails.length)),
          bcc: bccEmails.slice(i, Math.min(i + batchSize, bccEmails.length))
        };
  
        // Add additional emails to BCC if there's space
        const remainingBccSpace = batchSize - batch.to.length - batch.cc.length - batch.bcc.length;
        if (remainingBccSpace > 0 && additionalEmails.length > 0) {
          const additionalBatch = additionalEmails.slice(i, Math.min(i + remainingBccSpace, additionalEmails.length));
          batch.bcc = [...batch.bcc, ...additionalBatch];
        }
  
        // Skip empty batches
        if (batch.to.length === 0 && batch.cc.length === 0 && batch.bcc.length === 0) {
          continue;
        }
  
        const mailOptions = {
          from: fromEmailSenderName ? `${fromEmailSenderName} <${fromEmail}>` : fromEmail,
          subject: subject,
          html: htmlContent,
          to: batch.to.length ? batch.to : undefined,
          cc: batch.cc.length ? batch.cc : undefined,
          bcc: batch.bcc.length ? batch.bcc : undefined,
          replyTo: replyToEmails,
        };
  
        try {
          const mailResult = await transporter.sendMail(mailOptions);
          successCount++;
          console.log(`Email with Nodemailer batch ${Math.floor(i / batchSize) + 1} sent successfully => `, mailResult);
        } catch (batchError) {
          console.error(`Error sendEmailWithNodemailer sending email batch ${Math.floor(i / batchSize) + 1}:`, batchError);
        }
      }
      return successCount > 0;
    } catch (error) {
      console.error('Error in sendEmailWithNodemailer main catch block:', error);
      return false;
    }
  }

module.exports.fetchJobRoleDetails = fetchJobRoleDetails;
module.exports.getJobPostBasedOnRole = getJobPostBasedOnRole;
module.exports.sendDynamicTemplateEmail = sendDynamicTemplateEmail;
