service: DOCUSIGN # service name

plugins:
  - serverless-domain-manager
  - serverless-prune-plugin # Plugin to maintain lambda versioning
  - serverless-offline # require plugins
  - "@haftahave/serverless-ses-template" # Plug-In to deploy SES templates

provider:
  name: aws
  runtime: nodejs18.x #nodejs run time
  stage: ${opt:stage} # get current stage name
  region: ${opt:region} #region in which to be deployed
  role: ${file(../config.${self:provider.stage}.json):lambdaRole} # Assign role to the lambda functions
  vpc:
    securityGroupIds: ${file(../config.${self:provider.stage}.json):securityGroupIds}
    subnetIds: ${file(../config.${self:provider.stage}.json):subnetIds}
custom:
  sesTemplates:
    addStage: true
    configFile: "./ses-email-templates/index.js"
    deployHook: "after:deploy:deploy"
    region: ${file(../config.${self:provider.stage}.json):sesRegion}
  customDomain:
    domainName: ${file(../config.${self:provider.stage}.json):customDomainName}
    basePath: "docusign"
    stage: ${self:provider.stage}
    createRoute53Record: true
    endpointType: "edge"
  prune:
    automatic: true
    number: 3

package:
  patterns:
    # Only exclude unnecessary Chromium binaries - keep the compressed ones needed for Lambda (AL2)
    - '!node_modules/@sparticuz/chromium/bin/**'
    - 'node_modules/@sparticuz/chromium/bin/chromium.br'
    - 'node_modules/@sparticuz/chromium/bin/fonts.tar.br'
    - 'node_modules/@sparticuz/chromium/bin/swiftshader.tar.br'
    - 'node_modules/@sparticuz/chromium/bin/al2.tar.br'
    # Exclude Puppeteer local downloads (if any)
    - '!node_modules/puppeteer-core/.local-chromium/**'
    - '!node_modules/puppeteer-core/.local-firefox/**'
    # Exclude source maps (not needed in production)
    - '!node_modules/**/*.map'
    - '!node_modules/**/*.ts'
    - '!node_modules/**/*.md'
    - '!node_modules/**/README*'
    - '!node_modules/**/LICENSE*'
    - '!node_modules/**/CHANGELOG*'
    - '!node_modules/**/.github/**'
    - '!node_modules/**/test/**'
    - '!node_modules/**/tests/**'
    - '!node_modules/**/docs/**'
    - '!node_modules/**/examples/**'

# Lambda functions
functions:
  rographql:
    handler: src/rohandler.graphql
    timeout: 29 # Lambda timeout
    events:
      - http:
          path: rographql
          method: post
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
          authorizer:
            arn: ${file(../config.${self:provider.stage}.json):authorizerARN}
            resultTtlInSeconds: 0
            type: request
    environment: # environment variables
      stageName: ${self:provider.stage}
      domainName: ${file(../config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      logoBucket: ${file(../config.${self:provider.stage}.json):logoBucket}
      sesTemplatesRegion: ${file(../config.${self:provider.stage}.json):sesTemplatesRegion}
      emailFrom: ${file(../config.${self:provider.stage}.json):emailFrom}
      webAddress: ${file(../config.${self:provider.stage}.json):webAddress}
      encryptedDocBucket: ${file(../config.${self:provider.stage}.json):encryptedDocBucket}
      documentsBucket: ${file(../config.${self:provider.stage}.json):documentsBucket}

  wographql:
    handler: src/wohandler.graphql
    timeout: 29 # Lambda timeout
    # layers:
      # - ${file(../config.${self:provider.stage}.json):awsChromeLambdaLayerARN}
    events:
      - http:
          path: wographql
          method: post
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
          authorizer:
            arn: ${file(../config.${self:provider.stage}.json):authorizerARN}
            resultTtlInSeconds: 0
            type: request
    environment: # environment variables
      stageName: ${self:provider.stage}
      domainName: ${file(../config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      emailTo: ${file(../config.${self:provider.stage}.json):emailTo}
      sesRegion: ${file(../config.${self:provider.stage}.json):sesRegion}
      logoBucket: ${file(../config.${self:provider.stage}.json):logoBucket}
      emailFrom: ${file(../config.${self:provider.stage}.json):emailFrom}
      sesTemplatesRegion: ${file(../config.${self:provider.stage}.json):sesTemplatesRegion}
      webAddress: ${file(../config.${self:provider.stage}.json):webAddress}
      encryptedDocBucket: ${file(../config.${self:provider.stage}.json):encryptedDocBucket}
      documentsBucket: ${file(../config.${self:provider.stage}.json):documentsBucket}

  noauthrographql:
    handler: src/noauthrohandler.graphql
    timeout: 29 # Lambda timeout
    events:
      - http:
          path: noauthrographql
          method: post
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
    environment: # environment variables
      stageName: ${self:provider.stage}
      domainName: ${file(../config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}

  noauthwographql:
    handler: src/noauthwohandler.graphql
    timeout: 29 # Lambda timeout
    # layers:
    #   - ${file(../config.${self:provider.stage}.json):awsChromeLambdaLayerARN}
    events:
      - http:
          path: noauthwographql
          method: post
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
    environment: # environment variables
      stageName: ${self:provider.stage}
      domainName: ${file(../config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      logoBucket: ${file(../config.${self:provider.stage}.json):logoBucket}
      emailFrom: ${file(../config.${self:provider.stage}.json):emailFrom}
      sesTemplatesRegion: ${file(../config.${self:provider.stage}.json):sesTemplatesRegion}
      webAddress: ${file(../config.${self:provider.stage}.json):webAddress}
      encryptedDocBucket: ${file(../config.${self:provider.stage}.json):encryptedDocBucket}
      documentsBucket: ${file(../config.${self:provider.stage}.json):documentsBucket}
      atsNameForIndeed: ${file(../config.${self:provider.stage}.json):atsNameForIndeed}

resources:
  Resources:
    ApiGatewayRestApi: # Map customized api gateway responses
      Type: AWS::ApiGateway::RestApi
      Properties:
        Name: ${self:service}-${self:provider.stage}

    GatewayResponse4XX: # statusCode 4XX series errorcode
      Type: "AWS::ApiGateway::GatewayResponse"
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: "ApiGatewayRestApi"
        ResponseType: DEFAULT_4XX # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "Forbidden." } }'

    GatewayResponse401: # statusCode 401
      Type: "AWS::ApiGateway::GatewayResponse"
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: "ApiGatewayRestApi"
        ResponseType: UNAUTHORIZED # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        StatusCode: "401" # API gateway default errorcode
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "Unauthorized request." } }'

    GatewayResponse5XX: # statusCode 5XX series error code
      Type: "AWS::ApiGateway::GatewayResponse"
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: "ApiGatewayRestApi"
        ResponseType: DEFAULT_5XX # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "API gateway timeout." } }'
