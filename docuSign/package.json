{"name": "docusign", "version": "1.0.0", "description": "docusign node modules", "main": "rohandler.js", "engines": {"node": "18.x", "npm": ">=8.0.0"}, "dependencies": {"@cksiva09/hrapp-corelib": "git+https://cksiva09:<EMAIL>/cksiva09/hrapp-corelib.git", "@cksiva09/validationlib": "^1.4.22", "@haftahave/serverless-ses-template": "^4.0.3", "@sparticuz/chromium": "126.0.0", "apollo-server": "^2.4.8", "apollo-server-lambda": "^2.15.0", "aws-sdk": "^2.1691.0", "cryptr": "^6.0.2", "graphql": "^15.1.0", "knex": "^0.20.15", "mimemessage": "^1.0.5", "moment": "^2.29.1", "mysql": "^2.18.0", "nodemailer": "^6.10.0", "path": "^0.12.7", "puppeteer-core": "22.12.0", "serverless-domain-manager": "^7.1.2", "serverless-prune-plugin": "^2.0.2"}, "devDependencies": {"serverless-offline": "^13.2.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "sls offline --stage dev --region ap-south-1 --reload<PERSON>andler", "local": "sls offline --stage local --region ap-south-1 --reload<PERSON>andler"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC"}