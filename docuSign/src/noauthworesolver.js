const retrieveSignatoryAndDocumentDetails=require('./noauthroresolvers/documentGenerator/retrieveSignatoryAndDocumentDetails');
const updateFileName=require('./noauthworesolvers/documentGenerator/updateFileName');
const updateDocumentDetails=require('./noauthworesolvers/documentGenerator/updateDocumentDetails');
const uploadEmployeeGeneratedDocument=require('./noauthworesolvers/documentGenerator/uploadEmployeeGeneratedDocument');
const updateOfferLetterStatus=require('./noauthworesolvers/documentGenerator/updateOfferLetterStatus');
const addUpdateEmployeeGeneratedDocument=require('./woresolvers/documentGenerator/addUpdateEmployeeGeneratedDocument');
// Define resolver
const resolvers = {
    Query: Object.assign({},
        retrieveSignatoryAndDocumentDetails
    ),
    Mutation: Object.assign({},
        updateFileName,
        updateDocumentDetails,
        uploadEmployeeGeneratedDocument,
        updateOfferLetterStatus,
        addUpdateEmployeeGeneratedDocument
    )
}
exports.resolvers = resolvers;
