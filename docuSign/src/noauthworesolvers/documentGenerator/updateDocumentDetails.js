//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require constants
const { defaultValues,systemLogs,formName, formIds } = require('../../../common/appconstants');
//Require moment
const moment = require('moment-timezone');
const axios = require('axios');

//Update the signatory and the document details
async function updateSignatoryAndDocuments(organizationDbConnection,ehrTables,context,args,i){
    try{
        //Get the generated document details for the document id
        let signedStatusCount = 0;
        let authorizerDetails;
        let documentFileName = '';
        let responseJson = '';
        if(i===3){
            console.log('Inside third iteration.');
            return 'clear-interval';
        }else{
            let generatedDocumentId = args.documentId;
            return(
            organizationDbConnection
            .transaction(function(trx){
                //Get the document details for the document id
                return(
                organizationDbConnection(ehrTables.empGeneratedDocuments + ' as EGD')
                .select('EGD.Document_Content as documentContent','EGD.Generated_Document_Id as generatedDocumentId','Signature_Lock as signatureLock',
                'EGD.Document_Name as documentName','GDC.Component_Inputs as authorizedSignatories', 'EGD.Candidate_Id')
                .leftJoin(ehrTables.generatedDocumentCustomComponents + ' as GDC',function() {
                    this.on('EGD.Generated_Document_Id', '=', 'GDC.Generated_Document_Id')
                        .on('GDC.Component_Name', '=', organizationDbConnection.raw('?', [defaultValues.authorizedSignatoryComponentName]))
                })
                .where('EGD.Generated_Document_Id',generatedDocumentId)
                .transacting(trx)
                .then(async(documentDetails) => {
                    //If the employee generated document exist
                    if(documentDetails && documentDetails.length > 0){
                        let signatureLock = documentDetails[0].Signature_Lock;
                        if(signatureLock > 0 && signatureLock !== args.authorizerDetails.signatureEmployeeId){
                            console.log('Signature locked.');
                            return 'not-clear-interval';
                        }else{
                            //Update the signature lock for the document
                            return(
                            organizationDbConnection(ehrTables.empGeneratedDocuments + ' as EGD')
                            .update({
                                Signature_Lock: args.authorizerDetails.signatureEmployeeId
                            })
                            .where('EGD.Generated_Document_Id',generatedDocumentId)
                            .transacting(trx)
                            .then(()=>{
                                authorizerDetails = documentDetails[0].authorizedSignatories;
                                //Check the authorizer details exist
                                if(authorizerDetails){
                                    authorizerDetails = JSON.parse(documentDetails[0].authorizedSignatories);
                                    if(authorizerDetails.length > 0){
                                        let currentAuthorizerIndex = '';
                                        //Find the current authorizer JSON from the result
                                        authorizerDetails.find(function(authorizerDetail, key){
                                            //Get the previous signature count
                                            if(authorizerDetail.status === 'Signed'){
                                                signedStatusCount+=1;
                                            }
                                            if(parseInt(authorizerDetail.signatureEmployeeId) === parseInt(args.authorizerDetails.signatureEmployeeId)){
                                                currentAuthorizerIndex = key;
                                                return currentAuthorizerIndex;
                                            }
                                        });
                                        authorizerDetails[currentAuthorizerIndex] = args.authorizerDetails;
                                        signedStatusCount +=1; //add the current signature count

                                        let updateDetails = {
                                            Component_Inputs: JSON.stringify(authorizerDetails)
                                        };
                                        //Update the signing authority custom component
                                        return(
                                        organizationDbConnection(ehrTables.generatedDocumentCustomComponents)
                                        .update(updateDetails)
                                        .where('Generated_Document_Id',generatedDocumentId)
                                        .where('Component_Name','Signing Authority')
                                        .transacting(trx)
                                        .then(async(updateResult) => {
                                            //If the signing authority details are updated.
                                            if(updateResult){
                                                //Get the time for the authorizer based on the authorizer timezone
                                                let authorizerCurrentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                                                /** Check the login employee current date response is string or not to validate whether the current date time 
                                                 * or the other error response is returned.*/
                                                authorizerCurrentDateTime = (typeof(authorizerCurrentDateTime) === 'string') ? authorizerCurrentDateTime : null;

                                                let employeeDocumentUpdateDetails = {
                                                    Signature_Lock:0,
                                                    Last_Updated_On:authorizerCurrentDateTime,
                                                    Last_Updated_By:args.authorizerDetails.signatureEmployeeId,
                                                };

                                                let documentStatus = '';
                                                if(signedStatusCount == authorizerDetails.length){
                                                    //Get the expiry time in UTC.Example: 2022-03-14 11:27:17
                                                    let currentUTC = moment.utc().format('YYYY-MM-DD HH:mm:ss');
                                                    documentFileName = generatedDocumentId+"/"+generatedDocumentId+'?'+currentUTC+'?'+documentDetails[0].documentName+'.pdf';
                                                    //Append the document link
                                                    employeeDocumentUpdateDetails.Document_Link = documentFileName;
                                                    documentStatus = 'Completed';
                                                }else{
                                                    documentStatus = 'Partially Signed';
                                                }
                                                employeeDocumentUpdateDetails.Status=documentStatus;

                                                //Update the document status and update document S3 path if exist                                        
                                                return(
                                                organizationDbConnection(ehrTables.empGeneratedDocuments)
                                                .update(employeeDocumentUpdateDetails)
                                                .where('Generated_Document_Id',generatedDocumentId)
                                                .transacting(trx)
                                                .then(async() => {
                                                    if(documentStatus.toLowerCase() === 'completed' && args.authorizerDetails.signatureKey.toLowerCase() === 'candidate'){
                                                        let currentUTC = moment.utc().format('YYYY-MM-DD HH:mm:ss');
                                                        organizationDbConnection(ehrTables.candidateRecruitmentInfo)
                                                        .update({Candidate_Status:21,Offer_Letter_Response_Date:currentUTC})
                                                        .where('Candidate_Id',args.authorizerDetails.signatureEmployeeId)
                                                        .transacting(trx)
                                                        .then(async(data) => {
                                                            let candidateInfo = await commonLib.func.getSourceName(organizationDbConnection, [args.authorizerDetails.signatureEmployeeId]);
                                                            console.log("The candidate source info:", candidateInfo);
                                                            if(candidateInfo?.length && candidateInfo[0].Source?.toLowerCase() === 'indeed' && candidateInfo[0].Indeed_Apply_Id) {
                                                                const result = await indeedDispositionSyncAPI(candidateInfo);
                                                                if (!result) {
                                                                    throw 'CDG0123';
                                                                }
                                                            }
                                                            console.log("status updated sucessfully",data)
                                                        }
                                                    )
                                                    .catch(err=>{
                                                        console.log('Error in the updateSignatoryAndDocuments function .catch block.',err);
                                                        throw err;
                                                    })
                                                }
                                                    const signatureKey = args.authorizerDetails.signatureKey==='Candidate';
                                                    //Form the system log params
                                                    let systemLogParam ={
                                                        action: systemLogs.roleUpdate,
                                                        userIp: context.User_Ip,
                                                        employeeId: signatureKey ? 0 : args.authorizerDetails.signatureEmployeeId,
                                                        formId: formIds.docuSign,
                                                        uniqueId: signatureKey ? args.authorizerDetails.signatureEmployeeId : documentDetails[0]?.Candidate_Id || 0,
                                                        isEmployeeTimeZone: 0,
                                                        formName: formName.docuSign,
                                                        message: `The ${args?.authorizerDetails?.signatureKey || ''} ${signatureKey ? args?.authorizerDetails?.signatureEmployeeName : ''} has been signed the ${documentDetails[0]?.documentName || ''} document. ${documentStatus ? ', and the status is '+documentStatus: ''}`,
                                                        organizationDbConnection: organizationDbConnection
                                                    };
                                                    //Call the function to add the system log
                                                    await commonLib.func.createSystemLogActivities(systemLogParam);
                                                    return 'success';
                                                })
                                                .catch(function (err) {
                                                    console.log('Error in update document status catch block', err)
                                                    throw err
                                                })
                                                )
                                            }else{
                                                console.log('Unable to update the signature details for the document id',generatedDocumentId,'result:',updateResult);
                                                throw 'CDG0115';
                                            }
                                        })
                                        )
                                    }else{
                                        console.log('Parsed authorizer details not exist.',authorizerDetails);
                                        throw ('CDG0111');
                                    }
                                }else{
                                    console.log('Authorizer details not exist.',authorizerDetails);
                                    throw ('CDG0111');
                                }
                            })
                            );
                        }
                    }else{
                        console.log('Generated document not exist.',generatedDocumentId);
                        throw('_EC0001');
                    }
                })
                )
            })
            .then((result) => {
                if(result === 'not-clear-interval'){
                    console.log('Signature lock is greater than zero.');
                    return {
                        result: result,
                        responseJson: ''
                    };
                }else{
                    console.log('All the details are updated.');
                    responseJson = {
                        authorizerDetails: authorizerDetails,
                        pdfDocumentName: documentFileName,
                        signedCount: signedStatusCount
                    }
                    return {
                        result: 'clear-interval',
                        responseJson: JSON.stringify(responseJson)
                    };
                }
            })
            .catch((updateCatchError) => {
                console.log('Error in the updateSignatoryAndDocuments function .catch block.',updateCatchError);
                let catchErrorCodes = ['CDG0115','CDG0111','_EC0001'];
                if(catchErrorCodes.includes(updateCatchError)){
                    throw updateCatchError;
                }else{
                    throw 'CDG0117';
                }
            })
            )
        }
    }catch(catchError){
        console.log('Error in the updateSignatoryAndDocuments() function in main catch block.',catchError);
        throw catchError;
    }
}

//Set the timeout for 2 seconds to call the function after that 2 seconds
async function waitForTwoSeconds(ms)  {
    return new Promise( resolve => { setTimeout(resolve, ms); });
}

//Function to execute the signature update function for 3 times based on the signature lock validation for every 2 seconds
async function waitUntilAndUpdateSignatory(organizationDbConnection,ehrTables,context,args) {
    let initialValue = 0;
    let timeOutInSeconds = 2000;
    return await new Promise((resolve,reject) => {
    const interval = setInterval(async() => {
        for(let i=initialValue; i<=2;){
            try{
                let response = await updateSignatoryAndDocuments(organizationDbConnection,ehrTables,context,args,i);
                console.log('Response from the updateSignatoryAndDocuments() function.',response,'i:',i)
                i++;
                if(i===3){
                    console.log('Loop is executed three times but the signature is still locked.So stop the execution')
                    clearInterval(interval);
                    clearTimeout(timeOutInSeconds);
                    reject('CDG0123');
                    break;
                }else{
                    if (response.result !== 'clear-interval') {
                        clearInterval(interval);
                        await waitForTwoSeconds(timeOutInSeconds);
                        continue;
                    }else{
                        console.log('clear interval');
                        clearInterval(interval);
                        clearTimeout(timeOutInSeconds);
                        resolve(response.responseJson);
                        break;
                    }
                };
            }catch(loopCatchError){
                console.log('Error in the setIntervalAndUpdateSignatory function loop block.',loopCatchError);
                clearInterval(interval);
                clearTimeout(timeOutInSeconds);
                reject(loopCatchError);
                break;
            }
        }
      }, 2000);
    });
}

//Update the signature details, document details and send email to next authorizer
module.exports.updateDocumentDetails = async (parent, args, context, info) => {
    console.log('Inside updateDocumentDetails() function.');
    let organizationDbConnection;
    let errResult;
    try{
        if(!(args.authorizerDetails && Object.keys(args.authorizerDetails).length > 0)
        || !(args.documentId)){
            throw('_EC0007');
        }else{
            organizationDbConnection = knex(context.connection.OrganizationDb);
            //Require table alias
            const { ehrTables } = commonLib.tableAlias;
            //Call this function to update the signature lock and update the signatures.
            let responseJson = await waitUntilAndUpdateSignatory(organizationDbConnection,ehrTables,context,args);
            //Destroy DB connection
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            //Return response
            return {errorCode:'',message:'Document details updated successfully',documentDetails:responseJson};
        }
    }catch(mainCatchErr) {
        console.log('Error in the updateDocumentDetails() function main catch block. ',mainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchErr, 'CDG0013');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    }
};

async function getAuthCredentialsForIndeed() {
    try {
      // Define the request URL
      const url = "https://apis.indeed.com/oauth/v2/tokens";
      const AWS = require('aws-sdk');
          
      // Create client for secrets manager
      let client = new AWS.SecretsManager({
          region: process.env.region
      });
      // Get secrets from aws secrets manager
      let secretKeys = await client.getSecretValue({ SecretId: process.env.dbSecretName }).promise();
      secretKeys = JSON.parse(secretKeys.SecretString);
      let indeedClientid = secretKeys.indeed_clientid;
      let indeedClientsecret = secretKeys.indeed_clientsecret;
      // Define the request headers
      const headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        Accept: "application/json",
      };
      // Define the request body
      const data = new URLSearchParams({
        scope: "employer_access",
        client_id: indeedClientid,
        client_secret: indeedClientsecret,
        grant_type: "client_credentials",
      });
        // Make the POST request using Axios
        const response = await axios.post(url, data, { headers });
        let authTokens = response.data;
        return authTokens?.access_token;
    } catch (err) {
      console.log('Error in getAuthCredentialsForIndeed main catch block', err)
      throw err
    }
  }

async function indeedDispositionSyncAPI(candidateInfo) {
    try {
        const formattedDate = moment().utc().format('YYYY-MM-DDTHH:mm:ss.SSZ');
        let indeedAccessToken = await getAuthCredentialsForIndeed();
        indeedAccessToken = "Bearer " + indeedAccessToken;
        const indeedData = {
            "input": {
            "dispositions": [
                {
                    dispositionStatus: "HIRED",
                    rawDispositionStatus: "Hired",
                    rawDispositionDetails: "",
                    identifiedBy: {
                    indeedApplyID: candidateInfo[0].Indeed_Apply_Id,
                    ittk: null
                    },
                    atsName: process.env.atsNameForIndeed,
                    statusChangeDateTime: formattedDate
                }
            ]
            }
        };
      const indeedUrl = "https://apis.indeed.com/graphql";
      query = `mutation Send($input : SendPartnerDispositionInput !) {
        partnerDisposition {
          send(input : $input) {
            numberGoodDispositions 
            failedDispositions {
              identifiedBy {
                indeedApplyID 
                ittk 
                alternateIdentifier {
                  jobIdentifier {
                    indeedJobKey 
                    atsApplicationIdentifier{
                      requisitionId 
                      companyName
                    }
                  }
                  jobSeekerIdentifier 
                    { 
                      indeedJobSeekerKey 
                      emailAddress 
                    }
                }
              }
              rationale
            }
          }
        }
      }
      `,
      variables = indeedData.input;
      let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: indeedUrl,
      headers: { 
          'Content-Type': 'application/json', 
          'Authorization': indeedAccessToken, 
      },
      data: JSON.stringify({
          query: query,
          variables: {
          input: indeedData.input
          }
        })
      };
  
      try {
          // Make the request using Axios
          let response = await Promise.resolve(axios.request(config));
          const responseMessage = JSON.stringify(response.data);
          console.log("Disposition payload:", indeedData.input);
          console.log("Disposition api response:", responseMessage);
          return true;
        } catch (error) {
          console.log('Error in indeedDispositionSyncAPI catch block', error);
          return false;
        }
    } catch (err) {
      console.log('Error in indeedDispositionSyncAPI main catch block', err)
      return false;
    }
  }