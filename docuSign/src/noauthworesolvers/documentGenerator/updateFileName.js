//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require moment
const moment = require('moment');
//Update the signature file name
module.exports.updateFileName = async (parent, args, context, info) => {
    console.log('Inside updateFileName() function.');
    let organizationDbConnection;
    let errResult;
    try{
        if(args.documentId && args.fileName && args.candidateId>=0
        && args.employeeId>=0 && ([0,1].includes(args.saveSignature))){
            organizationDbConnection = knex(context.connection.OrganizationDb);
            //Require table alias
            const { ehrTables } = commonLib.tableAlias;
            //Inputs
            let documentId = args.documentId;
            let fileName = args.fileName;
            let candidateId = args.candidateId;
            let employeeId = args.employeeId;
            let uniqueId,isCandidate,fieldName;
            if(employeeId){
                uniqueId = employeeId;
                isCandidate = 0;
                fieldName = 'Employee_Id';
            }else{
                uniqueId = candidateId;
                isCandidate = 1;
                fieldName = 'Candidate_Id';
            }

            let currentDateTime = moment().tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss');
            //Save the signature s3 path in the document signatory details table
            return(
            organizationDbConnection(ehrTables.generatedDocumentSignatureDetails)
            .update({
                Signature_Path: documentId+'/'+fileName,
                Last_Updated_On:currentDateTime,
                Last_Updated_By:uniqueId
            })
            .where('Generated_Document_Id',documentId)
            .where('Authorizer_Id',uniqueId)
            .then(()=>{
                //If the signature has to be saved
                if(args.saveSignature === 1){
                    return(
                    organizationDbConnection(ehrTables.signatureDetails)
                    .select('File_Name')
                    .where(fieldName,uniqueId)
                    .then((signatureResult) => {
                        let signatureUpdateDetails = {
                            File_Name: fileName
                        };
                        //Insert the signature s3 path
                        if(signatureResult && signatureResult.length === 0){
                            if(isCandidate){
                                signatureUpdateDetails.Candidate_Id = uniqueId;
                            }else{
                                signatureUpdateDetails.Employee_Id = uniqueId;
                            }
                            return(
                            organizationDbConnection(ehrTables.signatureDetails)
                            .insert(signatureUpdateDetails)
                            .then(async(insertSignatureResult) => {
                                if(insertSignatureResult && insertSignatureResult.length > 0){
                                    //Destroy DB connection
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    return {errorCode: '',message: 'File Name updated successfully'};
                                }else{
                                    throw 'CDG0127';
                                }
                            })
                            )
                        }else{
                            console.log('No need to update the signature image as it already exists.');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return {errorCode: '',message: 'File Name updated successfully'};
                        }
                    })
                    )
                }else{
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return {errorCode: '',message: 'File Name updated successfully'};
                }
            })
            .catch(updateCatchError =>{
                console.log('Error in the updateFileName function .catch block.',updateCatchError);
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                errResult = commonLib.func.getError(updateCatchError, 'CDG0126');
                throw new ApolloError(errResult.message,errResult.code);//Return error response
            })
            );
        }else{
            console.log('Invalid inputs.',args);
            throw '_EC0007';
        }
    }catch(updateFileNameMainCatchErr) {
        console.log('Error in the updateFileName() function main catch block. ',updateFileNameMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(updateFileNameMainCatchErr, 'CDG0018');
        throw new ApolloError(errResult.message,errResult.code);//Return error response
    }
};