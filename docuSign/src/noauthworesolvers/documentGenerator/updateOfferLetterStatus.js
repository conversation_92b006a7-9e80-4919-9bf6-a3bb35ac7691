//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require moment
const moment = require('moment-timezone');
const { getOrgDetails} = require('../../../common/documentGeneratorCommonFunctions');
const { ehrTables } = commonLib.tableAlias;
//Require app constants
const { awsSesTemplates, formIds, formName, systemLogs } = require('../../../common/appconstants');

//Update the signature details, document details and send email to next authorizer
module.exports.updateOfferLetterStatus = async (parent, args, context, info) => {
    console.log('Inside updateOfferLetterStatus() function.');
    let organizationDbConnection;
    let errResult
    try {
        //Get the generated document details for the document id
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Require table alias
        const { ehrTables } = commonLib.tableAlias;
        //Call this function to update the signature lock and update the
        let generatedDocumentId = args.documentId;
        let checkIfSignedAlreadyValue= await checkIfSignedAlready(organizationDbConnection,generatedDocumentId,ehrTables);
        if(!checkIfSignedAlreadyValue){
            throw 'CDG0112'
        }
        let candidateIds=checkIfSignedAlreadyValue?checkIfSignedAlreadyValue:args.candidateId;
        let currentUTC = moment.utc().format('YYYY-MM-DD HH:mm:ss');
        //Get the document details for the document id
        console.log(args.status,"args.status")
        return (
            organizationDbConnection
                .transaction(async function(trx){
            await organizationDbConnection(ehrTables.empGeneratedDocuments + ' as EGD')
                .update({
                    Status: args.status
                })
                .transacting(trx)
                .where('EGD.Generated_Document_Id', generatedDocumentId)
                .then(async() => {
                    let updateObject= {Offer_Letter_Response_Date:currentUTC}
                    updateObject.Candidate_Status=args.signatureKey && args.signatureKey ==="Candidate"?22:28;
                     let result =await organizationDbConnection(ehrTables.candidateRecruitmentInfo)
                                                .update(updateObject)
                                                .where('Candidate_Id',candidateIds)
                                                .transacting(trx);
                    if(!result){
                        console.log('Error while update candidate recruitment status');
                        throw 'CDG0168'
                    }
                    await sendMailDeclineDocument(organizationDbConnection,candidateIds, args,context);
                })
                .catch(err => {
                    console.log('Error in the updateOfferLetterStatus() function catch block. ',err);
                    throw err;
                })
            }).then( async () => {
                  //Destroy DB connection
              organizationDbConnection ? organizationDbConnection.destroy() : null;
              //Return response
               return { errorCode: '', message: 'offer letter status updated successfully'};

            }).catch(err=>{
                console.log('Error in the updateOfferLetterStatus() function catch block. ',err);
                    throw err;
            })
        )




    } catch (catchError) {
        console.log('Error in the updateOfferLetterStatus() function main catch block. ',catchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(catchError, 'CDG0168');
        //Return error response
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function checkIfSignedAlready(organizationDbConnection,generatedDocumentId,ehrTables){
    let document= await organizationDbConnection(ehrTables.empGeneratedDocuments + ' as EGD')
                .select('Status','Candidate_Id')
                .where('EGD.Generated_Document_Id', generatedDocumentId);

                if(document && document.length>0){
                    if(document[0].Status.toLowerCase()==='completed'){
                        return 0;
                    }
                    else{
                    return document[0].Candidate_Id;
                }
                }
                
}


async function sendMailDeclineDocument(organizationDbConnection,candidateIds, args,context){ 

    let employeeDocument =  await organizationDbConnection(ehrTables.empGeneratedDocuments + ' as EGD')
    .select('EGD.Document_Link', 'EGD.Document_Content', 'EJ1.Emp_Email as intiatorJobMail', 'EGD.Document_Name as documentName',
    'EGD.Document_Link as documentFileName', 'GDC.Component_Inputs as authorizedSignatories',
    'EGD.Added_By as initiatorId', organizationDbConnection.raw("CONCAT_WS(' ', EPI1.Emp_First_Name, EPI1.Emp_Last_Name) as initiatorName"),)
    .leftJoin(ehrTables.empPersonalInfo +' as EPI1', 'EPI1.Employee_Id', 'EGD.Added_By')
    .leftJoin(ehrTables.empJob+' as EJ1','EJ1.Employee_Id', 'EGD.Added_By')
    .leftJoin(ehrTables.generatedDocumentCustomComponents + ' as GDC',function() {
        this.on('EGD.Generated_Document_Id', '=', 'GDC.Generated_Document_Id')
            .on('GDC.Component_Name', '=', organizationDbConnection.raw('?', ['Signing Authority']))
    })
    .where('EGD.Generated_Document_Id', args.documentId);

   
    if(employeeDocument && employeeDocument.length >0){

        // declined not required document
        
         // require aws-sdk to use aws services
        // const AWS = require('aws-sdk')
        // const s3 = new AWS.S3({ region: process.env.region });

        // let presignedUrl;
        // if(employeeDocument[0]?.documentFileName){
        //     const s3params = {
        //         Bucket: process.env.encryptedDocBucket,
        //         Key: employeeDocument[0]?.documentFileName,
        //         Expires: (24 * 60 * 60) // URL expiry time in seconds
        //     };
        //     presignedUrl = await s3.getSignedUrl('getObject', s3params);
        // }


        let mailIds = [], subject="",intiatorMailIds=[];
      

        let authorizerDetails = employeeDocument[0].authorizedSignatories;
        authorizerDetails = authorizerDetails ? JSON.parse(authorizerDetails) : [];

        //Get the authorizer email ids
        for(let j in authorizerDetails){
            let authorizer = authorizerDetails[j];
            if(['Manager or Admin','Employee'].includes(authorizer.signatureKey)){
               
                const managerDetails = await organizationDbConnection(ehrTables.empPersonalInfo + ' as EPI')
                .select('EJ.Emp_Email as managerJobEmail', organizationDbConnection.raw("CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Last_Name) as managerName"))
                .leftJoin(ehrTables.empJob+' as EJ','EJ.Employee_Id', 'EPI.Employee_Id')
                .where('EPI.Employee_Id',authorizer.signatureEmployeeId)
                .then();

                if(managerDetails && managerDetails.length > 0){
                    
                    employeeDocument[0].managerJobEmail = managerDetails[0].managerJobEmail
                    employeeDocument[0].managerName = managerDetails[0].managerName
                }
                

            }else if(authorizer.signatureKey === 'Candidate'){
               
                const candidateDetails = await organizationDbConnection(ehrTables.candidatePersonalInfo + ' as CPI')
                .select('CPI.Personal_Email as candidateEmail', 'CJ.Emp_Email as candidateJobEmail', 'JP.Job_Post_Name as jobTitle', 
                    organizationDbConnection.raw("CONCAT_WS(' ', CPI.Emp_First_Name, CPI.Emp_Last_Name) as candidateName"),)
                    .leftJoin(ehrTables.candidateJob+' as CJ','CJ.Candidate_Id', 'CPI.Candidate_Id')
                    .leftJoin(ehrTables.candidateRecruitementInfo + " as CRI", 'CRI.Candidate_Id', 'CPI.Candidate_Id')
                    .leftJoin(ehrTables.jobPost +' as JP', 'JP.Job_Post_Id', 'CRI.Job_Post_Id')
                .where('CPI.Candidate_Id',authorizer.signatureEmployeeId)
                .then();

                if(candidateDetails && candidateDetails.length > 0){
                    
                    employeeDocument[0].candidateEmail = candidateDetails[0].candidateEmail;
                    employeeDocument[0].candidateJobEmail = candidateDetails[0].candidateJobEmail
                    employeeDocument[0].candidateName = candidateDetails[0].candidateName
                    employeeDocument[0].jobTitle = candidateDetails[0].jobTitle
                }
            }
        }
        let declinedBy = '';
        let initiatorName= employeeDocument[0]. initiatorName
        let managerName=employeeDocument[0].managerName
        console.log(employeeDocument[0],"employeeDocument[0]")
        let mailContent=`I am writing to inform you that ${employeeDocument[0]?.candidateName} has decided to decline the job offer/withdraw application for the ${employeeDocument[0]?.jobTitle} position. Although we were excited about the possibility of having him/her/them join our team, he/she/they have chosen to pursue another opportunity at this time.`
        let initiatorMailContent=`I am writing to inform you that ${managerName} has decided to reject the job offer for the ${employeeDocument[0]?.candidateName} for the  ${employeeDocument[0]?.jobTitle} position. Please discuss with the manager for the next steps.`
        if(args && args.signatureKey && args.signatureKey ==="Candidate"){

            employeeDocument[0].managerJobEmail ? mailIds.push(employeeDocument[0].managerJobEmail) : null;
            employeeDocument[0].intiatorJobMail ? intiatorMailIds.push(employeeDocument[0].intiatorJobMail) : null;

            subject = "Candidate "+employeeDocument[0]?.candidateName+ " | " + employeeDocument[0]?.jobTitle;
            declinedBy = employeeDocument[0]?.candidateName;
        }else if(args && args.signatureKey && args.signatureKey === "Manager or Admin"){

            employeeDocument[0].intiatorJobMail ? intiatorMailIds.push(employeeDocument[0].intiatorJobMail) : null;
            subject = "Manager "+employeeDocument[0]?.managerName +" | "+ employeeDocument[0]?.jobTitle;
            declinedBy = employeeDocument[0]?.managerName;

        }
        var orgDetails = await getOrgDetails(context.Org_Code,organizationDbConnection);
            var organizationAddress =  [], companyName =""
                companyName = orgDetails.Org_Name;
                organizationAddress = await commonLib.func.getOrgAddress(organizationDbConnection,employeeDocument[0].initiatorId);
            
            var street1 = organizationAddress.length > 0 ? organizationAddress[0].Street1 : '';
            var street2 = organizationAddress.length > 0 ? organizationAddress[0].Street2 : '';
            var pinCode = organizationAddress.length > 0 ? organizationAddress[0].Pincode : '';
            var cityName = organizationAddress.length > 0 ? organizationAddress[0].City_Name : '';
            var stateName = organizationAddress.length > 0 ? organizationAddress[0].State_Name : '';
            var countryName = organizationAddress.length > 0 ? organizationAddress[0].Country_Name : '';

            var reportLogoS3Path =null;
                if(context && (context.partnerid=="entomo" || context.Partnerid=="entomo")){

                }else{

                     reportLogoS3Path =  orgDetails.Report_LogoPath ? process.env.domainName + '_upload/' + context.Org_Code+ '_tmp/logos/' + orgDetails.Report_LogoPath : ''; 
                }
        if(mailIds.length || intiatorMailIds.length){
    let templateName;
    let mailidss;
    let emailContent;
    if(args && args.signatureKey && args.signatureKey ==="Candidate"){
        mailidss=mailIds
        emailContent=mailContent;
        templateName=awsSesTemplates.jobOfferRejectionManager
        }else if(args && args.signatureKey && args.signatureKey === "Manager or Admin"){
            emailContent=initiatorMailContent;
            templateName=awsSesTemplates.jobOfferRejectionInitiator
            mailidss=intiatorMailIds;
        }
        let defaulTemplateData = {
            "emailSubject": `Job Offer Declined by ${subject}`,
            "orgLogo": reportLogoS3Path?reportLogoS3Path:'',
            "subTitle": ` ${employeeDocument[0]?.documentName} - document has been declined by ${declinedBy}.`,
            "redirectionUrl": "",
            "hideDisplayUrl": false,
            "hrappSupportEmail": orgDetails.HR_Admin_Email_Address ? orgDetails.HR_Admin_Email_Address :'',
            "managerName":managerName,
            "companyName":companyName,
            "street1":street1,
            "street2":street2,
            "city": cityName,
            "pinCode": pinCode,
            "state": stateName,
            "country": countryName,
            "offerLetterInitiator":initiatorName,
            "mailContent":emailContent,
            "recruitersName":initiatorName
        }

            let params = {
                "Source": process.env.emailFrom,
                "Template": templateName,
                "Destinations": [
                    { "Destination": { "BccAddresses": mailidss } }
                ],
                "DefaultTemplateData": JSON.stringify(defaulTemplateData),
            }
            try{
                const AWS = require('aws-sdk')
                AWS.config.update({
                    region: process.env.sesTemplatesRegion
                });
                const ses = new AWS.SES({
                    apiVersion: "2010-12-01"
                });
                let response = await ses.sendBulkTemplatedEmail(params).promise()
                console.log(response)

                console.log('Signed document as attachment email sent successfully.');
                let systemLogParam = {
                    action: systemLogs.roleUpdate,
                    userIp: context?.User_Ip || '-',
                    employeeId: 0,
                    formId: formIds.docuSign,
                    formName: formName.docuSign,
                    message: `${employeeDocument[0]?.documentName || ''} has been declined by ${declinedBy}.`,
                    organizationDbConnection: organizationDbConnection,
                    uniqueId: args.candidateId,
                    isEmployeeTimeZone: 0,
                };
                await commonLib.func.createSystemLogActivities(systemLogParam);

            }catch(emailCatchError){
                console.error('Error while sending the email in the sendMailSignedDocument() function catch block.',emailCatchError);
                throw 'PBP0105';
            }
        
        }
    }


}