//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require moment
const moment = require('moment-timezone');
//Require common function
const {uploadTextDocumentInS3, copyS3Document, uploadPuppeteerPDFToS3} = require('../../../common/uploadDocument');
//Require common function
const {formSignatureEmailInputsAndMail,getTemplateName,getOrgDetails} = require('../../../common/documentGeneratorCommonFunctions');
const {sendDynamicTemplateEmail }=require('../../../common/commonFunctions');
//Require constants
const { defaultValues,urlEncryption,awsSesTemplates, formIds } = require('../../../common/appconstants');
//Require knex to make DB connection
const knex = require('knex');
const { ehrTables } = commonLib.tableAlias;

//Update the signature details, document details and send email to next authorizer
module.exports.uploadEmployeeGeneratedDocument = async (parent, args, context, info) => {
    console.log('Inside uploadEmployeeGeneratedDocument function.');
    let organizationDbConnection;
    let errResult;
    try{
        if(args.signatureFileName && args.documentId && args.documentName
        && args.authorizerDetails && args.authorizerDetails.length > 0
        && args.signatoryEmployeeId && args.signatoryKey && args.signedCount
        && (args.signedCount == args.authorizerDetails.length && args.pdfDocumentName ||
        (!(args.pdfDocumentName) && (args.signedCount !== args.authorizerDetails.length)))){
            organizationDbConnection = knex(context.connection.OrganizationDb);

            let signaturePath = args.signatureFileName;
            let generatedDocumentId = args.documentId;
            let documentName = args.documentName;
            let authorizerDetails = args.authorizerDetails;
            let signatoryEmployeeId = args.signatoryEmployeeId;
            let signatoryKey = args.signatoryKey;
            let signedCount = args.signedCount;
            let pdfDocumentName = args.pdfDocumentName;
            let documentAttachment = args.documentAttachment;
            let documentSubTypeId = args.documentSubTypeId;
            
            const AWS = require('aws-sdk');
            // Create object for s3 bucket
            const s3 = new AWS.S3({ region: process.env.region });
            let signatureKey = process.env.domainName+"/"+context.Org_Code+"/"+"Document Generator/";
            //Get the signature image
            let imageFileName = signatureKey+signaturePath;
            let imageS3Params = {
                Bucket: process.env.encryptedDocBucket, 
                Key: imageFileName
            };
            let imageDocument = await s3.getObject(imageS3Params).promise();
            imageDocument = imageDocument.Body.toString('base64');
            let imageBase64 = `data:image/png;base64,${imageDocument}`;

            //Get the text file
            let textFileName = signatureKey+generatedDocumentId+"/"+generatedDocumentId+'?'+documentName+'.txt';
            let textS3Params = {
                Bucket: process.env.encryptedDocBucket, 
                Key: textFileName
            };
            let fileData = await s3.getObject(textS3Params).promise();
            let content=fileData.Body;
            //Convert buffer to string
            let inputFileContent=content.toString('utf8');

            let orgDetails = await commonLib.func.getOrgDetails(context.Org_Code, organizationDbConnection, 0);
            let Date_Format = orgDetails.Date_Format ? orgDetails.Date_Format : 'DD/MM/YYYY';
            
            const dateTimeInUTC = moment.utc().format(Date_Format + ' HH:mm:ss');
            
            let signatorySignDate = dateTimeInUTC + ' (UTC)';

            let imageSrc = '';
            let signatorySignDateExp = '';
            if(signatoryKey==='Manager or Admin'){
                imageSrc = "https://s3.ap-south-1.amazonaws.com/s3.hrapp-dev-public-images/Signature-Images/managerOrAdmin.png";
                signatorySignDateExp = /\{Date Signed \(Manager \/ Admin\)\}/;
            }else if(signatoryKey==='Employee'){
                imageSrc = "https://s3.ap-south-1.amazonaws.com/s3.hrapp-dev-public-images/Signature-Images/employee.png";
                signatorySignDateExp = /\{Date Signed \(Employee\)\}/;
            }else{
                imageSrc = "https://s3.ap-south-1.amazonaws.com/s3.hrapp-dev-public-images/Signature-Images/candidate.png";
                signatorySignDateExp = /\{Date Signed \(Candidate\)\}/;
            }
            if(signatorySignDateExp.test(inputFileContent)){
                inputFileContent = inputFileContent.replace(signatorySignDateExp, signatorySignDate);
            }
            //Update image in the text document
            let imageRegex = new RegExp(imageSrc, "g");
            let newTextContent = inputFileContent.replace(imageRegex,imageBase64);
            let uploadTextFileName = signatureKey+generatedDocumentId+"/"+generatedDocumentId+'?'+documentName+'.txt';
            //Upload the new text document
            await uploadTextDocumentInS3(s3,uploadTextFileName,newTextContent);

            //Generate PDF with header/footer after each signature upload
            //Get document header and footer from emp_generated_documents table
            let documentDetails = await organizationDbConnection(ehrTables.empGeneratedDocuments + ' as EGD')
                .select('EGD.Document_Header as documentHeader', 'EGD.Document_Footer as documentFooter', 'EGD.Document_Link as documentLink')
                .where('EGD.Generated_Document_Id', generatedDocumentId)
                .first();

            let documentHeader = documentDetails?.documentHeader || '';
            let documentFooter = documentDetails?.documentFooter || '';
            let documentLink = documentDetails?.documentLink || '';

            //Generate intermediate PDF after this signature (if header/footer exists)
            if (documentLink) {
                let intermediatePdfFileName = signatureKey+documentLink;
                console.log('Generating intermediate PDF with header/footer after signature upload.');
                let pdfUploadResult = await uploadPuppeteerPDFToS3(
                    intermediatePdfFileName,
                    newTextContent,
                    documentHeader,
                    documentFooter
                );

                if (pdfUploadResult && pdfUploadResult.result === 'failure') {
                    console.error('Intermediate PDF upload failed:', pdfUploadResult.error);
                }
            }

            const notificationSettings =  await organizationDbConnection('recruitment_settings')
            .select('Auto_Follow_On_Signature', 'auto_send_signed_document').first();

            //Get the authorizer who are not signed
            let otherAuthorizerDetails = authorizerDetails.filter(item=>item.status!='Signed');
            //Send the email to candidate when all the signatories other than the candidates signed the document
            let signatureEmailInputs = {};
            if(otherAuthorizerDetails.length > 0 && notificationSettings?.Auto_Follow_On_Signature?.toLowerCase() === "yes"){
                let getOtherSignatureDetails = otherAuthorizerDetails.filter(item=>item.signatureKey!=='Candidate' && item.signatureKey!=='Employee');
                let getCandidateDetails = otherAuthorizerDetails.filter(item=>item.signatureKey==='Candidate' || item.signatureKey === 'Employee');
          
                let templateName=await getTemplateName(organizationDbConnection,args.documentId);   
                signatureEmailInputs = {
                    generatedDocumentId: generatedDocumentId,
                    documentName: documentName,
                    authorizerDetails: otherAuthorizerDetails,
                    encryptionKey: urlEncryption.encryptionKey,
                    documentAttachment: documentAttachment,
                    documentSubTypeId: documentSubTypeId,
                    Template_Name: templateName,
                    orgCode: context.Org_Code,
                    candidateId: null,
                    signatureUrlPath: defaultValues.signatureUrlPath,
                    awsSesTemplateName: awsSesTemplates.sendDocumentLinkToSign,
                    addedOn: moment().tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss'),
                    addedBy: signatoryEmployeeId
                };

                if(getOtherSignatureDetails.length > 0){
                    await formSignatureEmailInputsAndMail(organizationDbConnection,signatureEmailInputs);
                } else if(getCandidateDetails.length === 1){
                    signatureEmailInputs.candidateId = getCandidateDetails[0].signatureEmployeeId;
                    await formSignatureEmailInputsAndMail(organizationDbConnection, signatureEmailInputs);
                } 
            } else{
                if(otherAuthorizerDetails.length > 0){
                    
                    const signedAuthorizerDetails = authorizerDetails.filter(item=>item.status=='Signed' && item.signatureEmployeeId === args.signatoryEmployeeId)[0];
                    await intiatorSendEmail(organizationDbConnection, generatedDocumentId, context, signedAuthorizerDetails);
                }
            }
            //If all the users signed then upload the pdf content in s3
            if(signedCount == authorizerDetails.length){
                //File Name: 1/1?2022-03-14 11:27:17?Appointment Letter.pdf
                let documentFileName = signatureKey+pdfDocumentName;

                //Get document header and footer from emp_generated_documents table
                let documentDetails = await organizationDbConnection(ehrTables.empGeneratedDocuments + ' as EGD')
                    .select('EGD.Document_Header as documentHeader', 'EGD.Document_Footer as documentFooter')
                    .where('EGD.Generated_Document_Id', generatedDocumentId)
                    .first();

                let documentHeader = '';
                let documentFooter = '';

                //Get header and footer from emp_generated_documents table
                if (documentDetails) {
                    documentHeader = documentDetails.documentHeader || '';
                    documentFooter = documentDetails.documentFooter || '';
                }

                //Upload the PDF using Puppeteer if header or footer exists, otherwise use existing method
                let message;
                console.log('Using Puppeteer PDF generation with header/footer support for signed document.');
                message = await uploadPuppeteerPDFToS3(
                    documentFileName,
                    newTextContent,
                    documentHeader,
                    documentFooter
                );

                if(message.result === 'success'){
                    organizationDbConnection = knex(context.connection.OrganizationDb);
                    await sendMailSignedDocument(organizationDbConnection, generatedDocumentId, documentFileName, documentName,context, notificationSettings);
                    if(args.documentAttachment) {
                        await addDocument(organizationDbConnection, authorizerDetails, documentFileName, context.Org_Code, args);
                    }
                }else{
                    console.error("Error while offer letter pdf document is not created.  ", message)
                    throw 'PBP0104';
                }
            }
            //Destroy DB connection
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return {errorCode:'',message:'File uploaded successfully.'};
        }else{
            console.log('Invalid inputs.',args);
            throw('_EC0007');
        }
    }catch(mainCatchErr) {
        console.log('Error in the uploadEmployeeGeneratedDocument() function main catch block. ',mainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchErr, 'CDG0019');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    }
}

async function sendMailSignedDocument(organizationDbConnection, documentId, documentFileName, documentName,context, notificationSettings){ 

    try{
        if(notificationSettings?.auto_send_signed_document?.toLowerCase() === 'yes'){
            
            let employeeDocument =  await organizationDbConnection(ehrTables.empGeneratedDocuments + ' as EGD')
            .select('EGD.Document_Link', 'EGD.Document_Content','DTE.Form_Id as formId', 'EJ1.Emp_Email as intiatorJobMail','EJ1.Employee_Id as initiatorId','GDC.Component_Inputs as authorizedSignatories')
            .leftJoin(ehrTables.empJob+' as EJ1','EJ1.Employee_Id', 'EGD.Added_By')
            .leftJoin(ehrTables.documentTemplateEngine+' as DTE','DTE.Title', 'EGD.Template_Name')
            .leftJoin(ehrTables.generatedDocumentCustomComponents + ' as GDC',function() {
                this.on('EGD.Generated_Document_Id', '=', 'GDC.Generated_Document_Id')
                    .on('GDC.Component_Name', '=', organizationDbConnection.raw('?', ['Signing Authority']))
            })
            .where('EGD.Generated_Document_Id',documentId);

            
            var orgDetails = await commonLib.func.getOrgDetails(context.Org_Code, organizationDbConnection, 1);

            var reportLogoS3Path =null;
            if(context && (context.partnerid=="entomo" || context.Partnerid=="entomo")){
            }else{
                reportLogoS3Path =  orgDetails.logoPath ?  orgDetails.logoPath: ''; 
            }

            // require aws-sdk to use aws services
            const AWS = require('aws-sdk');
            const s3 = new AWS.S3({ region: process.env.region });

            const params = {
                Bucket: process.env.encryptedDocBucket,
                Key: documentFileName,
                Expires: (24 * 60 * 60) // URL expiry time in seconds
            };
            const presignedUrl = await s3.getSignedUrl('getObject', params);

            if(Boolean(presignedUrl) && Array.isArray(employeeDocument) && employeeDocument.length > 0){
        
                let authorizerDetails = employeeDocument[0].authorizedSignatories;
                authorizerDetails = authorizerDetails ? JSON.parse(authorizerDetails) : [];

                        //Get the authorizer email ids
                for(let j in authorizerDetails){

                let authorizer = authorizerDetails[j];
                if(['Manager or Admin','Employee'].includes(authorizer.signatureKey)){

                    const managerDetails = await organizationDbConnection(ehrTables.empPersonalInfo + ' as EPI')
                    .select('EJ.Emp_Email as managerJobEmail', organizationDbConnection.raw("CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Last_Name) as managerName"))
                    .leftJoin(ehrTables.empJob+' as EJ','EJ.Employee_Id', 'EPI.Employee_Id')
                    .where('EPI.Employee_Id',authorizer.signatureEmployeeId)

                    if(managerDetails && managerDetails.length > 0){
                        employeeDocument[0].managerJobEmail = managerDetails[0].managerJobEmail
                        employeeDocument[0].managerName = managerDetails[0].managerName
                    }


                }else if(authorizer.signatureKey === 'Candidate'){

                    const candidateDetails = await organizationDbConnection(ehrTables.candidatePersonalInfo + ' as CPI')
                    .select(
                        'CPI.Personal_Email as candidateEmail',
                        'CJ.Emp_Email as candidateJobEmail',
                        'JP.Job_Post_Name as jobTitle',
                        'JP.Job_Post_Id',
                        organizationDbConnection.raw("CONCAT_WS(' ', CPI.Emp_First_Name, CPI.Emp_Last_Name) as candidateName"),
                        organizationDbConnection.raw("GROUP_CONCAT(DISTINCT CASE WHEN EJ.Emp_Email IS NOT NULL AND TRIM(EJ.Emp_Email) != '' THEN EJ.Emp_Email END SEPARATOR ',') as onboardingSpecialistEmails")
                    )
                    .leftJoin(ehrTables.candidateJob+' as CJ','CJ.Candidate_Id', 'CPI.Candidate_Id')
                    .leftJoin(ehrTables.candidateRecruitementInfo + " as CRI", 'CRI.Candidate_Id', 'CPI.Candidate_Id')
                    .leftJoin(ehrTables.jobPost +' as JP', 'JP.Job_Post_Id', 'CRI.Job_Post_Id')
                    .leftJoin('job_post_onboard_specialist as JPOS', 'JPOS.Job_Post_Id', 'JP.Job_Post_Id')
                    .leftJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'JPOS.Onboard_Specialist_Id')
                    .where('CPI.Candidate_Id', authorizer.signatureEmployeeId)
                    .groupBy('CPI.Candidate_Id')

                    if(candidateDetails && candidateDetails.length > 0){

                        employeeDocument[0].candidateEmail = candidateDetails[0].candidateEmail;
                        employeeDocument[0].candidateJobEmail = candidateDetails[0].candidateJobEmail
                        employeeDocument[0].candidateName = candidateDetails[0].candidateName
                        employeeDocument[0].jobTitle = candidateDetails[0].jobTitle

                        // Process onboarding specialist emails from the combined query
                        const specialistEmails = candidateDetails[0].onboardingSpecialistEmails || '';
                        employeeDocument[0].onboardingspecialistemails = specialistEmails ?
                            specialistEmails.split(',').filter(email => email && email.trim() !== '') :
                            [];
                    }
                }
                }
                let mailIds = [],candidateEmailIds=[];
                let emailContent1=`Please be informed that ${employeeDocument[0].candidateName}, candidate for ${employeeDocument[0].jobTitle}, has accepted the ${documentName}.`;
                let emailContent2=null;
                let emailContent3=null;
                employeeDocument[0].candidateEmail ? candidateEmailIds.push(employeeDocument[0].candidateEmail) :  
                employeeDocument[0].candidateJobEmail ?  candidateEmailIds.push(employeeDocument[0].candidateJobEmail) : null;

                employeeDocument[0].managerJobEmail ? mailIds.push(employeeDocument[0].managerJobEmail) : null;
                employeeDocument[0].intiatorJobMail ? mailIds.push(employeeDocument[0].intiatorJobMail) : null;
                
                if(employeeDocument[0].onboardingspecialistemails && employeeDocument[0].onboardingspecialistemails.length > 0) {
                        mailIds = mailIds.concat(employeeDocument[0].onboardingspecialistemails);
                }
                if(!employeeDocument[0].formId || employeeDocument[0].formId!==16){
                    employeeDocument[0].managerJobEmail ?  candidateEmailIds.push(employeeDocument[0].managerJobEmail) : null;
                    employeeDocument[0].intiatorJobMail ?  candidateEmailIds.push(employeeDocument[0].intiatorJobMail) : null;
                }

                let defaulTemplateData = {
                    emailSubject: `${documentName} Accepted ${employeeDocument[0]?.candidateName || ''} ${employeeDocument[0]?.jobTitle || ''}`,
                    orgLogo: '',
                    title1: '',
                    title2: '',
                    centerImage: '',
                    subTitle: `${documentName} - document has been completed by all participants.`,
                    redirectionUrl: presignedUrl || '',
                    hideDisplayUrl: true,
                    buttonText: 'Open the Document',
                    footer: '',
                    supportEmail: orgDetails.hrAdminEmailAddress ? orgDetails.hrAdminEmailAddress :''
                };
                
                if (!employeeDocument[0]?.formId || employeeDocument[0].formId !== 16) {
                    defaulTemplateData.emailSubject = `Document ${documentName} signed by all participants`;
                }
                let defaulTemplateData1 = {
                    emailSubject: `${documentName} Accepted ${employeeDocument[0]?.candidateName || ''} ${employeeDocument[0]?.jobTitle || ''}`,
                    orgLogo: reportLogoS3Path || '',
                    emailContent1: emailContent1 || '',
                    emailContent2: emailContent2 || '',
                    emailContent3: emailContent3 || '',
                    subTitle: `${documentName} - document has been completed by all participants.`,
                    redirectionUrl: presignedUrl || '',
                    buttonText: 'Open the Document',
                    footer: '',
                    hrappSupportEmail: orgDetails.hrAdminEmailAddress ? orgDetails.hrAdminEmailAddress :''
                };
                const params = [{
                    "Source": process.env.emailFrom,
                    "Template": "CommonTemplate",
                    "Destinations": [
                        { "Destination": { "BccAddresses": candidateEmailIds } }
                    ],
                    "DefaultTemplateData": JSON.stringify(defaulTemplateData),
                },
                {
                    "Source": process.env.emailFrom,
                    "Template": "OfferLetterAccepted",
                    "Destinations": [
                        { "Destination": { "BccAddresses": mailIds } }
                    ],
                    "DefaultTemplateData": JSON.stringify(defaulTemplateData1),
                }
            
            ]
            for(i=0;i<2;i++){
                if ((!employeeDocument[0]?.formId || employeeDocument[0].formId !== 16)&& i===1) {
                     continue;
                }
                try{
                    AWS.config.update({
                        region: process.env.sesTemplatesRegion
                    });
                    const ses = new AWS.SES({
                        apiVersion: "2010-12-01"
                    });
                    let response = await ses.sendBulkTemplatedEmail(params[i]).promise();

                    console.log('Signed document as attachment email sent successfully.');
                }catch(emailCatchError){
                    console.error('Error while sending the email in the sendMailSignedDocument() function catch block.',emailCatchError);
                    throw 'PBP0105';
                }
            }
            }
        }
    }catch(error){
        console.error('Error while automatically email send signatories and initiator with signed document as attachment sendMailSignedDocument() function catch block. ',error);
        throw 'PBP0110';
    }
    
}


async function intiatorSendEmail(organizationDbConnection, documentId, context, signaturedDetails){
    try{
        console.log('Inside intiatorSendEmail function.');        
        const AWS = require('aws-sdk');

        let orgDetails = await commonLib.func.getOrgDetails(context.Org_Code, organizationDbConnection, 1);
        let reportLogoS3Path = context && (context.partnerid=="entomo" || context.Partnerid=="entomo") ? '' : orgDetails?.logoPath || ''; 
      
        let employeeDocument =  await organizationDbConnection(ehrTables.empGeneratedDocuments + ' as EGD')
        .select('EGD.Document_Name', 'EGD.Template_Name', 'EJ1.Emp_Email as intiatorEmail', 'EJ1.Employee_Id')
        .leftJoin(ehrTables.empJob +' as EJ1','EJ1.Employee_Id', 'EGD.Added_By')
        .leftJoin(ehrTables.documentTemplateEngine+' as DTE','DTE.Title', 'EGD.Template_Name')
        .where('EGD.Generated_Document_Id', documentId).first();

        if(!employeeDocument || !employeeDocument?.intiatorEmail){
            console.log('Employee document or intiator email not found.');
            return;
        }

        let sendEmailNotificationsParams = {
            organizationDbConnection: organizationDbConnection,
            templateConfig: { categoryId: 24, formId: 134 },
            context: { Org_Code: context.Org_Code, Employee_Id: employeeDocument.Employee_Id },
            mailConfiguration: {
                sourceEmail: process.env.emailFrom,
                replyToEmails: [],
                bucketName: process.env.encryptedDocBucket,
                region: process.env.region
            },
            additionalPlaceholderData: {
                orgLogo: orgDetails.logoPath ? orgDetails.logoPath : '',
                documentName: employeeDocument.Document_Name,
                signedBy: signaturedDetails.signatureEmployeeName,
                signedOn: moment().format('YYYY-MM-DD'),
                signedKey: signaturedDetails.signatureKey,
                signatureStatus:signaturedDetails.status
            },
        }
        if(["Manager or Admin", "Employee"].includes(signaturedDetails.signatureKey)){
            sendEmailNotificationsParams.employeeId = signaturedDetails.signatureEmployeeId;
        }else if(["Candidate"].includes(signaturedDetails.signatureKey)){
            sendEmailNotificationsParams.candidateId = signaturedDetails.signatureEmployeeId;
        }

        let sendEmailNotificationsResponse = await sendDynamicTemplateEmail(sendEmailNotificationsParams);
        if (!sendEmailNotificationsResponse) {

            let mailIds = [employeeDocument.intiatorEmail];

            let defaulTemplateData = {
                emailSubject: `${employeeDocument.Document_Name} Signed for ${signaturedDetails.signatureKey}`,
                orgLogo: reportLogoS3Path,
                emailContent1: `The ${employeeDocument.Document_Name} document for ${signaturedDetails.signatureEmployeeName} has been successfully signed on ${moment().format('YYYY-MM-DD')}.`,
                emailContent2: 'You may now proceed with the next steps as required.',
                emailContent3: '',
                subTitle: '',
                redirectionUrl: '',
                buttonText: '',
                footer: '',
                hrappSupportEmail: orgDetails.hrAdminEmailAddress ? orgDetails.hrAdminEmailAddress :''
            };

            const emailTemplate = {
                "Source": process.env.emailFrom,
                "Template": "OfferLetterAccepted",
                "Destinations": [
                    { "Destination": { "BccAddresses": mailIds } }
                ],
                "DefaultTemplateData": JSON.stringify(defaulTemplateData),
            }

            AWS.config.update({
                region: process.env.sesTemplatesRegion
            });
            const ses = new AWS.SES({ apiVersion: "2010-12-01"});
            let response = await ses.sendBulkTemplatedEmail(emailTemplate).promise();

            console.log('Default email intiator sent successfully.');
        }

    } catch(error){
        console.error('Error while intiatorSendEmail function catch block. ', error);
    }
}



async function addDocument(organizationDbConnection, authorizerDetails, documentFileName, orgCode, args){

   try {
        console.log("Inside addDocument() function", documentFileName)
        let getPersonDetails = authorizerDetails.filter(item=>item.signatureKey==='Candidate' || item.signatureKey === 'Employee');  // flag to attach document
        let signatureId = getPersonDetails[0]?.signatureEmployeeId;

        let documentSubTypeDetails = await organizationDbConnection('document_sub_type')
        .select('document_type.Document_Type_Id', 'document_sub_type.Document_Sub_Type_Id', 'document_sub_type.Document_Sub_Type', 'document_type.Category_Id')
        .leftJoin('document_type', 'document_type.Document_Type_Id', 'document_sub_type.Document_Type_Id')
        .where('document_sub_type.Document_Sub_Type_Id', args.documentSubTypeId)   // where document_subtypeid captured from document_sub_type table
        
        if(documentSubTypeDetails && documentSubTypeDetails.length){
            
            let tableName = getPersonDetails[0]?.signatureKey === 'Candidate' ? 'candidate_document_category' : 'emp_document_category';
            let fileName = `${signatureId}?${new Date().getTime()}?${documentSubTypeDetails[0].Category_Id}?${args.documentName}.pdf`; 
            let pdfDocumentName = `${process.env.domainName}/${orgCode}/Employees Document Upload/${fileName}`;
            await copyS3Document(pdfDocumentName, documentFileName);
           // await uploadDocumentInS3(pdfDocumentName, documentFileName, process.env.documentsBucket);
        
            let documentData = {};
            if(tableName === 'candidate_document_category') {
                documentData = {
                    Candidate_Id: signatureId,
                    Category_Id: documentSubTypeDetails[0].Category_Id,
                    Document_Type_Id: documentSubTypeDetails[0].Document_Type_Id,
                    Sub_Document_Type_Id: documentSubTypeDetails[0].Document_Sub_Type_Id,
                    Document_Name: documentSubTypeDetails[0].Document_Sub_Type,
                    Added_On : moment.utc().format("YYYY-MM-DD HH:mm:ss")
                }
            } else {
                documentData = {
                    Employee_Id: signatureId,
                    // Category_Id: documentSubTypeDetails[0].Category_Id,
                    // Document_Type_Id: documentSubTypeDetails[0].Document_Type_Id,
                    Document_Sub_Type_Id: documentSubTypeDetails[0].Document_Sub_Type_Id,
                    Document_Name: documentSubTypeDetails[0].Document_Sub_Type,
                    Added_On : moment.utc().format("YYYY-MM-DD HH:mm:ss")
                }
            }

            let docTableName = getPersonDetails[0]?.signatureKey === 'Candidate' ? 'candidate_documents' : 'emp_documents';
            let insertData = await organizationDbConnection(tableName).insert(documentData);
            console.log("My inserteddd data:", insertData);              
            if (insertData && fileName) {
                await organizationDbConnection(docTableName).delete()
                .where('Document_Id', insertData[0]);
        
                await organizationDbConnection(docTableName)
                .insert({
                    'Document_Id': insertData[0],
                    'File_Name': fileName,
                    'File_Size': 0
                })  
                console.log(`Doc-sign addDocument added ${docTableName} table`)
            }                           
        }
    } catch(err){
        console.error("Error while addDocument function main catch block", err)
    }
}
