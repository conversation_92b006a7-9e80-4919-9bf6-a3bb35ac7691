//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
//Require constants
const { formName,systemLogs, formIds } = require('../../../common/appconstants');
//Require validation functions
const { validateAddUpdateDocumentTemplateInputs } = require('../../../common/documentTemplateValidation');
//Require common function
const { getDocumentTemplateFields } = require('../../../common/documentGeneratorCommonFunctions');
let moment = require('moment-timezone');

//Add and update the document template
module.exports.addUpdateDocumentTemplate = async (parent, args, context, info) => {
    console.log('Inside addUpdateDocumentTemplate function.');
    let organizationDbConnection;
    let errResult;
    let validationError={};
    try{
        //Require table alias
        const { ehrTables } = commonLib.tableAlias;

        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Get the login employee - document generator form access
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection,loginEmployeeId,formName.docuSign,'','UI');        
        let documentTemplateId = (args.documentTemplateId) ? args.documentTemplateId : 0;
        //Check add or update rights exist or not based on the input
        if(Object.keys(checkRights).length>0 && ((documentTemplateId === 0 && checkRights.Role_Add === 1)
        || (documentTemplateId > 0 && checkRights.Role_Update === 1) )) {
            let isEdit= 0;
            if(documentTemplateId > 0){
                isEdit=1;
            }
            validationError = await validateAddUpdateDocumentTemplateInputs(args, isEdit);
            //Check validation error exist or not
            if(Object.keys(validationError).length ===0){
                let templateTitleExistQuery = organizationDbConnection(ehrTables.documentTemplateEngine)
                .count('Document_Template_Id as templateIdCount')
                .where('Title',args.title);
                //If the template id exist
                if(documentTemplateId){
                    templateTitleExistQuery = templateTitleExistQuery.whereNot('Document_Template_Id',documentTemplateId);
                }
                //Validate the template title exist in the table or not
                return(
                templateTitleExistQuery
                .then(async(titleExistResponse) => {
                    if(titleExistResponse.length > 0 && titleExistResponse[0].templateIdCount === 0){
                        //Get the employee timezone based on location
                        let loginEmployeeCurrentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                        let updateDetails ={
                            Title: args.title,
                            Registered_Business_Address: args.registeredBusinessAddress,
                            Template_Content: args.templateContent,
                            Form_Id: args?.formId || null,
                            Category: args?.category || null,
                            Report_Id: args.reportId && args.reportId.length ? JSON.stringify(args.reportId) : null,
                            Document_Attachment: args?.documentAttachment || null,
                            Generate_Doc_For_Onboarding: args?.generateDocForOnboarding || null,
                            Document_Subtype_Id: args?.documentSubTypeId || null,
                            Description: args?.description || null,
                            Page_Scope: args?.pageScope || null,
                            Template_Header: args?.templateHeader || null,
                            Template_Footer: args?.templateFooter || null,
                            Custom_Footer_Id: args?.customFooterId || null,
                            Custom_Header_Id: args?.customHeaderId || null,
                        };

                        //Get the template fields from the template content
                        let templateFields = await getDocumentTemplateFields(args.templateContent);

                        //Concat the signatory key names. Example: Candidate, Employee, Manager or Admin
                        if(templateFields && templateFields.length > 0){
                            templateFields = templateFields.concat(args.signatoryKeys);
                        }else{
                            templateFields = [];
                            templateFields = args.signatoryKeys;
                        }
                        updateDetails.Template_Fields = (templateFields && templateFields.length > 0) ? JSON.stringify(templateFields) : null;

                        //Use transaction to ensure atomicity of reset and insert/update operations
                        return(
                        organizationDbConnection
                        .transaction(async function(trx){
                            //If generateDocForOnboarding is 1, set all other records to 0 within transaction
                            if(args?.generateDocForOnboarding === 1){
                                await organizationDbConnection(ehrTables.documentTemplateEngine)
                                .update({ Generate_Doc_For_Onboarding: 0 })
                                .where('Generate_Doc_For_Onboarding', 1)
                                .transacting(trx);
                            }

                            //Update the document template details
                            if(documentTemplateId>0){
                                updateDetails.Updated_On = loginEmployeeCurrentDateTime;
                                updateDetails.Updated_By = loginEmployeeId;

                                await organizationDbConnection(ehrTables.documentTemplateEngine)
                                .update(updateDetails)
                                .where('Document_Template_Id',documentTemplateId)
                                .transacting(trx);

                                //Return operation type and message for logging
                                return {
                                    operationType: 'update',
                                    documentTemplateId: documentTemplateId,
                                    message: `The ${args.title} document template has been updated.`
                                };
                            }else{
                                //Insert the document template details
                                updateDetails.Added_On = loginEmployeeCurrentDateTime;
                                updateDetails.Added_By = loginEmployeeId;

                                console.log(updateDetails)

                                let insertResponse = await organizationDbConnection(ehrTables.documentTemplateEngine)
                                .insert(updateDetails)
                                .transacting(trx);

                                documentTemplateId = insertResponse[0];

                                //Return operation type and message for logging
                                return {
                                    operationType: 'add',
                                    documentTemplateId: documentTemplateId,
                                    message: `The ${args.title} document template has been added.`
                                };
                            }
                        })
                        .then(async(transactionResult) => {
                            //After transaction commits successfully, log the activity
                            let systemLogParam = {
                                action: 'Document Template ' + (transactionResult.operationType === 'update' ? systemLogs.roleUpdate : systemLogs.roleAdd),
                                userIp: context.User_Ip,
                                employeeId: loginEmployeeId,
                                formId: formIds.docuSign,
                                organizationDbConnection: organizationDbConnection,
                                uniqueId: transactionResult.documentTemplateId,
                                message: transactionResult.message,
                                isEmployeeTimeZone: 0,
                            };
                            //Call the function to add the system log (outside transaction)
                            await commonLib.func.createSystemLogActivities(systemLogParam);

                            //Destroy DB connection after all operations complete
                            organizationDbConnection ? organizationDbConnection.destroy() : null;

                            //Return success response
                            return {
                                errorCode: '',
                                message: transactionResult.operationType === 'update'
                                    ? 'Document template updated successfully.'
                                    : 'Document template added successfully.'
                            };
                        })
                        .catch(function(transactionError){
                            //Destroy DB connection on error
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            throw transactionError;
                        })
                        )
                    }else{
                        console.log('Title already exists.',titleExistResponse);
                        throw 'CDG0002';
                    }
                })
                .catch(function (updateTemplateCatchError) {
                    console.log('Error in addUpdateDocumentTemplate function catch block', updateTemplateCatchError);
                    errResult = commonLib.func.getError(updateTemplateCatchError, 'CDG0102');
                    //Destroy DB connection on error
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    throw new ApolloError(errResult.message,errResult.code);//Return error response
                })
                );
            }else{
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                throw 'IVE0000';
            }
        }
        else{
            console.log('Login employee id does not have add or update access to document generator form.');
            //Destroy DB connection
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            throw (documentTemplateId === 0 ? '_DB0101' : '_DB0102');
        }
    }catch(documentTemplateMainCatchErr) {
        console.log('Error in the addUpdateDocumentTemplate function main catch block. ',documentTemplateMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (documentTemplateMainCatchErr === 'IVE0000') {
            console.log('Validation error in the addUpdateDocumentTemplate function',validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        } else{
            errResult = commonLib.func.getError(documentTemplateMainCatchErr, 'CDG0003');
            throw new ApolloError(errResult.message,errResult.code);//Return error response
        }
    }
};