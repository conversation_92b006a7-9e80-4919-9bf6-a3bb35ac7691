//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require constants
const { formName,systemLogs,formIds } = require('../../../common/appconstants');

//Delete the employee generated document
module.exports.deleteEmployeeGeneratedDocuments = async (parent, args, context, info) => {
    console.log('Inside deleteEmployeeGeneratedDocuments() function.');
    let organizationDbConnection;
    let errResult;
    try{
        //Require table alias
        const { ehrTables } = commonLib.tableAlias;
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Get the login employee - document generator form access
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection,loginEmployeeId,formName.docuSign,'','UI');
        let generatedDocumentId = (args.generatedDocumentId) ? args.generatedDocumentId : 0;
        //Check delete rights exist or not
        if(Object.keys(checkRights).length>0 && checkRights.Role_Delete === 1) {
            //Validate the generated document id
            if(generatedDocumentId <= 0){
                throw '_EC0007';
            }else{

                const documentData = await organizationDbConnection(ehrTables.empGeneratedDocuments).where('Generated_Document_Id', generatedDocumentId).first();

                //Delete the employee generated document
                return(
                organizationDbConnection(ehrTables.empGeneratedDocuments)
                .delete()
                .where('Generated_Document_Id',generatedDocumentId)
                .whereNot('Status','Completed')
                .then(async(deleteResponse) => {
                    if(deleteResponse){
                        //Log message: Delete Docu Sign  - 47
                        let systemLogParam = {
                            action: systemLogs.roleDelete,
                            userIp: context.User_Ip,
                            employeeId: loginEmployeeId,
                            formId: formIds.docuSign,
                            formName: formName.docuSign,
                            message: `The ${documentData.Document_Name} document has been deleted the ${documentData.Status} status.`,
                            organizationDbConnection: organizationDbConnection,
                            uniqueId: documentData.Candidate_Id,
                            isEmployeeTimeZone: 0,
                        };
                        //Call the function to add the system log
                        await commonLib.func.createSystemLogActivities(systemLogParam);
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        //Return success response
                        return {errorCode: '',message:'Employee generated document deleted successfully.'};
                    }else{
                        throw '_EC0006';
                    }
                })
                .catch(function (deleteDocumentCatchError) {
                    console.log('Error in deleteEmployeeGeneratedDocuments() function .catch() block', deleteDocumentCatchError);
                    errResult = commonLib.func.getError(deleteDocumentCatchError, 'CDG0105');
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    throw new ApolloError(errResult.message,errResult.code);//Return error response
                })
                );
            }
        }
        else{
            console.log('Login employee id does not have the delete access to document generator form.',checkRights);
            throw '_DB0103';
        }
    }catch(documentTemplateMainCatchErr) {
        console.log('Error in the deleteEmployeeGeneratedDocuments() function main catch block. ',documentTemplateMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(documentTemplateMainCatchErr, 'CDG0006');
        throw new ApolloError(errResult.message,errResult.code);//Return error response
    }
};