//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require constants
const { formName,systemLogs } = require('../../../common/appconstants');

//Delete the document template
module.exports.deleteDocumentTemplate = async (parent, args, context, info) => {
    console.log('Inside deleteDocumentTemplate() function.');
    let organizationDbConnection;
    let errResult;
    try{
        //Require table alias
        const { ehrTables } = commonLib.tableAlias;

        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Get the login employee - document generator form access
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection,loginEmployeeId,formName.docuSign,'','UI');
        let documentTemplateId = (args.documentTemplateId) ? args.documentTemplateId : 0;
        //Check delete rights exist or not
        if(Object.keys(checkRights).length>0 && checkRights.Role_Delete === 1) {
            //Validate the document template id
            if(documentTemplateId <= 0){
                throw '_EC0007';
            }else{
                //Check if document template is referenced in report_definitions table
                let reportDefinitionsCheck = await organizationDbConnection('report_definitions')
                    .select('Report_Id').where('Report_Header_Id', documentTemplateId)
                    .orWhere('Report_Footer_Id', documentTemplateId).first()

                if(reportDefinitionsCheck){
                    console.log('Document template is currently being used in report definitions and cannot be deleted.');
                   throw 'CTL0107';
                }

                //Delete the document template
                return(
                organizationDbConnection(ehrTables.documentTemplateEngine)
                .delete()
                .where('Document_Template_Id',documentTemplateId)
                .then(async(deleteResponse) => {
                    if(deleteResponse){
                        //Log message: Delete Document Template  - 47
                        let systemLogParam = {
                            action: systemLogs.roleDelete,
                            userIp: context.User_Ip,
                            employeeId: loginEmployeeId,
                            formName: '',
                            trackingColumn: 'Document Template',
                            organizationDbConnection: organizationDbConnection,
                            uniqueId: documentTemplateId
                        };
                        //Call the function to add the system log
                        await commonLib.func.createSystemLogActivities(systemLogParam);
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        //Return success response
                        return {errorCode: '',message:'Document template deleted successfully.'};
                    }else{
                        throw '_EC0006';
                    }
                })
                .catch(function (deleteTemplateCatchError) {
                    console.log('Error in deleteDocumentTemplate() function .catch() block', deleteTemplateCatchError);
                    errResult = commonLib.func.getError(deleteTemplateCatchError, 'CDG0103');
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    throw new ApolloError(errResult.message,errResult.code);//Return error response
                })
                );
            }
        }
        else{
            console.log('Login employee id does not have the delete access to document generator form.');
            throw '_DB0103';
        }
    }catch(documentTemplateMainCatchErr) {
        console.log('Error in the deleteDocumentTemplate() function main catch block. ',documentTemplateMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(documentTemplateMainCatchErr, 'CDG0004');
        throw new ApolloError(errResult.message,errResult.code);//Return error response
    }
};