//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
//Require constants
const { formName,systemLogs,urlEncryption,defaultValues,awsSesTemplates, formIds } = require('../../../common/appconstants');
//Require validation functions
const { validateAddUpdateDocumentInputs } = require('../../../common/documentTemplateValidation');
//Require common functions
const { formSignatureEmailInputsAndMail } = require('../../../common/documentGeneratorCommonFunctions');
//Require common function
const {uploadTextDocumentInS3,copyS3Document,uploadPuppeteerPDFToS3} = require('../../../common/uploadDocument');
let moment = require('moment-timezone');

//Add and update the document generator
module.exports.addUpdateEmployeeGeneratedDocument = async (parent, args, context, info) => {
    console.log('Inside addUpdateEmployeeGeneratedDocument() function.');
    let organizationDbConnection;
    let errResult;
    let validationError={};
    try{
        //Require table alias
        const { ehrTables } = commonLib.tableAlias;

        // Check if this is an unauthenticated request (skipAuth flag set by noauth handler)
        const skipAuth = context.skipAuth === true;
        // Use context.Employee_Id if authenticated, otherwise use args.employeeId for noauth
        let loginEmployeeId = skipAuth ? (args.employeeId || null) : context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let generatedDocumentId = (args.generatedDocumentId) ? args.generatedDocumentId : 0;
        let hasAccess = false;

        if (skipAuth) {
            // Unauthenticated request - skip access rights check
            console.log('Unauthenticated request (skipAuth=true) - skipping access rights check');
            hasAccess = true;
        } else {
            // Authenticated request - check employee access rights
            let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection,loginEmployeeId,formName.docuSign,'','UI');
            //Check add or update rights exist or not based on the input
            hasAccess = Object.keys(checkRights).length>0 && ((generatedDocumentId === 0 && checkRights.Role_Add === 1)
                || (generatedDocumentId > 0 && checkRights.Role_Update === 1));
        }

        if(hasAccess){
            let isEdit= 0;
            if(generatedDocumentId > 0){
                isEdit=1;
            }
            validationError = await validateAddUpdateDocumentInputs(args,isEdit);
            //Check validation error exist or not - Generated_Document_Id
            if(Object.keys(validationError).length ===0){
                let updateResult = '';
                let systemLogParam = '';
                let customComponentInputs;
                //Get the employee timezone based on location
                let loginEmployeeCurrentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');

                return(
                organizationDbConnection
                .transaction(async function(trx){
                    let updateDetails ={
                        Employee_Id: args.employeeId,
                        Candidate_Id:args.candidateId,
                        Template_Id: args.templateId,
                        Template_Name: args.templateName,
                        Document_Name: args.documentName,
                        Registered_Business_Address: args.registeredBusinessAddress,
                        Document_Content: args.documentContent,
                        Status: args.status,
                        Document_Header: args.documentHeader || null,
                        Document_Footer: args.documentFooter || null,
                        Page_Scope: args.pageScope || null,
                    };
                    
                    //Update the document generator details
                    if(generatedDocumentId>0){
                        updateDetails.Last_Updated_On = loginEmployeeCurrentDateTime;
                        updateDetails.Last_Updated_By = loginEmployeeId;
                        updateResult = 
                        await organizationDbConnection(ehrTables.empGeneratedDocuments)
                        .update(updateDetails)
                        .where('Generated_Document_Id',generatedDocumentId)
                        .transacting(trx)
                        .then((updateResult)=> { 
                            if(updateResult){
                                return 'success';
                            }else{
                                return 'notupdated';
                            }

                        });

                        const documentData = await organizationDbConnection(ehrTables.empGeneratedDocuments)
                        .where('Generated_Document_Id', generatedDocumentId).first();

                        const message = documentData.Status === args.status ? `The ${args.documentName} document has been updated the ${args.status} status.`
                        : `The ${args.documentName} document has been updated, The status changed from ${documentData.Status} to ${args.status}.`;
                        //Log message: Update Docu Sign  - 47
                        systemLogParam = {
                            action: systemLogs.roleUpdate,
                            message: message
                        };
                    }else{
                        //Insert the document generator details
                        updateDetails.Added_On = loginEmployeeCurrentDateTime;
                        updateDetails.Added_By = loginEmployeeId;
                        updateResult = 
                        await organizationDbConnection(ehrTables.empGeneratedDocuments)
                        .insert(updateDetails)
                        .transacting(trx)
                        .then((insertedDocumentId)=> { 
                            generatedDocumentId = insertedDocumentId[0];

                            if(args.customComponents.length > 0){
                                customComponentInputs = args.customComponents.map(field => ({
                                    Generated_Document_Id: generatedDocumentId,
                                    Component_Name: field.componentName,
                                    Component_Inputs: field.componentInputs
                                }))
                                
                                return(
                                    organizationDbConnection(ehrTables.generatedDocumentCustomComponents)
                                    .insert(customComponentInputs)
                                    .transacting(trx)
                                    .then(() => {
                                       
                                        return( 
                                            addDocumentSubType(organizationDbConnection, trx, args.templateName, 
                                            loginEmployeeId, loginEmployeeCurrentDateTime)
                                            .then(()=>{
                                                return 'success';
                                            })
                                        )
                                       
                                    })
                                )
                            }else{
                                return( 
                                    addDocumentSubType(organizationDbConnection, trx, args.templateName, 
                                    loginEmployeeId, loginEmployeeCurrentDateTime)
                                    .then(()=>{
                                        return 'success';
                                    })
                                )
                            }
                        });

                        //Log message: Add Docu Sign  - 47
                        systemLogParam = {
                            action: systemLogs.roleAdd,
                            message: `The ${args.documentName} document has been prepared and added the ${args.status} status.`
                        };
                    }

                    if(updateResult === 'success'){
                        return 'success';
                    }else if(updateResult === 'notupdated'){
                        throw '_EC0006';
                    }else{
                        console.log('Error occurred while adding or updating the generated documents.')
                        throw 'CDG0106';
                    }
                })
                .then( async () => {
                    systemLogParam.userIp= context.User_Ip;
                    systemLogParam.employeeId= loginEmployeeId;
                    systemLogParam.formId = formIds.docuSign;
                    systemLogParam.formName= formName.docuSign,
                    systemLogParam.organizationDbConnection= organizationDbConnection;
                    systemLogParam.uniqueId= args.candidateId;
                    systemLogParam.isEmployeeTimeZone=0;
                    
                    //Call the function to add the system log
                    await commonLib.func.createSystemLogActivities(systemLogParam);

                    if(['In Review','Completed'].includes(args.status)){
                        //Get the expiry time in UTC.Example: 2022-03-14 11:27:17
                        let documentSignatureKey = process.env.domainName+"/"+context.Org_Code+"/Document Generator/";
                        //File Name: 1/1?2022-03-14 11:27:17?Appointment Letter.txt
                        let textDocumentFileName = generatedDocumentId+"/"+generatedDocumentId+'?'+args.documentName+'.txt';
                        const AWS = require('aws-sdk');
                        // Create object for s3 bucket
                        const s3 = new AWS.S3({ region: process.env.region });
                        await uploadTextDocumentInS3(s3,documentSignatureKey+textDocumentFileName,args.documentContent);
                    }
                    //If the status is 'In Review' and authorizer details exist
                    if(args.status === 'In Review' && args.signingAuthorities){
                        //Generate initial PDF with header/footer if provided (before any signatures)
                        //Require moment
                        const moment = require('moment');

                        //Get the expiry time in UTC.Example: 2022-03-14 11:27:17
                        let currentUTC = moment.utc().format('YYYY-MM-DD HH:mm:ss');
                        let documentSignatureKey = process.env.domainName+"/"+context.Org_Code+"/Document Generator/";
                        //File Name: 1/1?2022-03-14 11:27:17?Appointment Letter.pdf
                        let documentFileName = generatedDocumentId+"/"+generatedDocumentId+'?'+currentUTC+'?'+args.documentName+'.pdf';
                        documentSignatureKey = documentSignatureKey+documentFileName;

                        //Upload the document in the S3 using Puppeteer for PDF generation with header and footer
                        let uploadResult;
                        // Use Puppeteer-based PDF generation
                        console.log('Using Puppeteer PDF generation with header/footer support for In Review status with signing authorities.');
                        uploadResult = await uploadPuppeteerPDFToS3(
                            documentSignatureKey,
                            args.documentContent,
                            args.documentHeader || '',
                            args.documentFooter || '',
                            process.env.encryptedDocBucket,
                            args.pageScope || 'All Pages'
                        );

                        // Check if upload was successful
                        if (uploadResult && uploadResult.result === 'failure') {
                            console.error('PDF upload failed:', uploadResult.error);
                            throw new Error('Failed to upload PDF document to S3');
                        }

                        //Update the document s3 file name for that document id in the table.
                        await organizationDbConnection(ehrTables.empGeneratedDocuments)
                            .update({
                                Document_Link: documentFileName
                            })
                            .where('Generated_Document_Id',generatedDocumentId);

                        let signingAuthorities = JSON.parse(args.signingAuthorities);

                        let signatureEmailInputs = {
                            generatedDocumentId: generatedDocumentId,
                            documentName: args.documentName,
                            authorizerDetails: signingAuthorities,
                            encryptionKey: urlEncryption.encryptionKey,
                            orgCode: context.Org_Code,
                            Template_Name: args.templateName,
                            signatureUrlPath: defaultValues.signatureUrlPath,
                            awsSesTemplateName: awsSesTemplates.sendDocumentLinkToSign,
                            candidateId:args.candidateId?args.candidateId:null,
                            addedOn: loginEmployeeCurrentDateTime,
                            addedBy: loginEmployeeId
                        };
                        await formSignatureEmailInputsAndMail(organizationDbConnection,signatureEmailInputs);

                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        //Return success response
                        return {errorCode: '',message:'Document template added or updated successfully and email has been sent to the authorizer.'};
                    }else if(args.status === 'In Review' && !args.signingAuthorities){
                        //If status is 'In Review' but no signing authorities, generate PDF using Puppeteer
                        //Require moment
                        const moment = require('moment');

                        //Get the expiry time in UTC.Example: 2022-03-14 11:27:17
                        let currentUTC = moment.utc().format('YYYY-MM-DD HH:mm:ss');
                        let documentSignatureKey = process.env.domainName+"/"+context.Org_Code+"/Document Generator/";
                        //File Name: 1/1?2022-03-14 11:27:17?Appointment Letter.pdf
                        let documentFileName = generatedDocumentId+"/"+generatedDocumentId+'?'+currentUTC+'?'+args.documentName+'.pdf';
                        documentSignatureKey = documentSignatureKey+documentFileName;

                        //Upload the document in the S3 using Puppeteer for PDF generation with header and footer
                        let uploadResult;
                        // Use Puppeteer-based PDF generation
                        console.log('Using Puppeteer PDF generation with header/footer support for In Review status without signing authorities.');
                        uploadResult = await uploadPuppeteerPDFToS3(
                            documentSignatureKey,
                            args.documentContent,
                            args.documentHeader || '',
                            args.documentFooter || '',
                            process.env.encryptedDocBucket,
                            args.pageScope || 'All Pages'
                        );

                        // Check if upload was successful
                        if (uploadResult && uploadResult.result === 'failure') {
                            console.error('PDF upload failed:', uploadResult.error);
                            throw new Error('Failed to upload PDF document to S3');
                        }

                        //Update the document s3 file name for that document id in the table.
                        return(
                        organizationDbConnection(ehrTables.empGeneratedDocuments)
                        .update({
                            Document_Link: documentFileName
                        })
                        .where('Generated_Document_Id',generatedDocumentId)
                        .then(async()=> {
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return success response
                            return {errorCode: '',message:'Document template added or updated successfully.', documentLink: documentFileName, generatedDocumentId: generatedDocumentId};
                        })
                        .catch(updateCatchError => {
                            console.log('Error while updating the document link in the emp_generated_documents table.',updateCatchError);
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            throw updateCatchError;
                        })
                        );
                    }else if(args.status === 'Completed'){
                        //Require moment
                        const moment = require('moment');

                        //Get the expiry time in UTC.Example: 2022-03-14 11:27:17
                        let currentUTC = moment.utc().format('YYYY-MM-DD HH:mm:ss');
                        let documentSignatureKey = process.env.domainName+"/"+context.Org_Code+"/Document Generator/";
                        //File Name: 1/1?2022-03-14 11:27:17?Appointment Letter.pdf
                        let documentFileName = generatedDocumentId+"/"+generatedDocumentId+'?'+currentUTC+'?'+args.documentName+'.pdf';
                        documentSignatureKey = documentSignatureKey+documentFileName;

                        //Upload the document in the S3 using Puppeteer for PDF generation with header and footer
                        let uploadResult;
                        // Use Puppeteer-based PDF generation
                        console.log('Using Puppeteer PDF generation with header/footer support.');
                        uploadResult = await uploadPuppeteerPDFToS3(
                            documentSignatureKey,
                            args.documentContent,
                            args.documentHeader || '',
                            args.documentFooter || '',
                            process.env.encryptedDocBucket,
                            args.pageScope || 'All Pages'
                        );

                        // Check if upload was successful
                        if (uploadResult && uploadResult.result === 'failure') {
                            console.error('PDF upload failed:', uploadResult.error);
                            throw new Error('Failed to upload PDF document to S3');
                        }

                        //Update the document s3 file name for that document id in the table.
                        return(
                        organizationDbConnection(ehrTables.empGeneratedDocuments)
                        .update({
                            Document_Link: documentFileName
                        })
                        .where('Generated_Document_Id',generatedDocumentId)
                        .then(async()=> {
                            //If documentAttachment flag is set and signingAuthorities do NOT exist, add document to employee/candidate documents
                            if(args.documentAttachment && !args.signingAuthorities && args.documentSubTypeId){
                                // When no signing authorities, use employee/candidate ID from args
                                let authorizerDetails = [{
                                    signatureKey: args.candidateId ? 'Candidate' : 'Employee',
                                    signatureEmployeeId: args.candidateId || args.employeeId
                                }];
                                // Pass the full S3 path (documentSignatureKey) instead of just documentFileName
                                await addDocument(organizationDbConnection, authorizerDetails, documentSignatureKey, context.Org_Code, args);
                            }
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return success response
                            return {errorCode: '',message:'Document template added or updated successfully.', documentLink: documentFileName, generatedDocumentId: generatedDocumentId};
                        })
                        );
                    }else{
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        //Return success response
                        return {errorCode: '',message:'Document template added or updated successfully.', documentLink: documentFileName ? documentFileName : null, generatedDocumentId: generatedDocumentId};
                    }
                })
                .catch(function (updateTemplateCatchError) {
                    console.log('Error in addUpdateEmployeeGeneratedDocument() function .catch() block', updateTemplateCatchError);
                    errResult = commonLib.func.getError(updateTemplateCatchError, 'CDG0106');
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    throw new ApolloError(errResult.message,errResult.code);//Return error response
                })
                );
            }else{
                throw 'IVE0000';
            }
        }
        else{
            console.log('Login employee id does not have add or update access to document generator form.');
            throw (generatedDocumentId === 0 ? '_DB0101' : '_DB0102');
        }
    }catch(documentTemplateMainCatchErr) {
        console.log('Error in the addUpdateEmployeeGeneratedDocument() function main catch block. ',documentTemplateMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        //If validation error exist
        if (documentTemplateMainCatchErr === 'IVE0000') {
            console.log('Validation error in the addUpdateEmployeeGeneratedDocument() function',validationError);
            errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });//Return error response
        } else{
            errResult = commonLib.func.getError(documentTemplateMainCatchErr, 'CDG0007');
            throw new ApolloError(errResult.message,errResult.code);//Return error response
        }
    }
};


async function addDocumentSubType(organizationDbConnection, trx, templateName, employeeId, loginEmployeeCurrentDateTime){

    try {
        console.log("Inside addDocumentSubType() function ", templateName)
            const exists = await organizationDbConnection('document_sub_type')
            .where('Document_Sub_Type', 'Offer Letter').first();

            if((!exists || exists.length === 0)){
                let documentTypeId = await organizationDbConnection('document_type').select('Document_Type_Id')
                .where('Document_Type', 'Employment Contract').first().then(documentType => documentType ? documentType.Document_Type_Id : 0);

                await organizationDbConnection('document_sub_type').transacting(trx)
                .insert({
                    Document_Type_Id: documentTypeId,
                    Document_Sub_Type: 'Offer Letter',
                    Mandatory: 'No',
                    Added_By: employeeId,
                    Added_On: loginEmployeeCurrentDateTime,
                })
            }else{
                console.log("Already exsit in template : ", templateName)
            }
    }catch(error){
        console.error('Error in the addDocumentSubType() function main catch block. ',error)
    }
}

//Add document to employee or candidate documents
async function addDocument(organizationDbConnection, authorizerDetails, documentFileName, orgCode, args){

   try {
        console.log("Inside addDocument() function", documentFileName)
        let getPersonDetails = authorizerDetails.filter(item=>item.signatureKey==='Candidate' || item.signatureKey === 'Employee');  // flag to attach document
        let signatureId = getPersonDetails[0]?.signatureEmployeeId;

        let documentSubTypeDetails = await organizationDbConnection('document_sub_type')
        .select('document_type.Document_Type_Id', 'document_sub_type.Document_Sub_Type_Id', 'document_sub_type.Document_Sub_Type', 'document_type.Category_Id')
        .leftJoin('document_type', 'document_type.Document_Type_Id', 'document_sub_type.Document_Type_Id')
        .where('document_sub_type.Document_Sub_Type_Id', args.documentSubTypeId)   // where document_subtypeid captured from document_sub_type table

        if(documentSubTypeDetails && documentSubTypeDetails.length){

            let tableName = getPersonDetails[0]?.signatureKey === 'Candidate' ? 'candidate_document_category' : 'emp_document_category';
            let fileName = `${signatureId}?${new Date().getTime()}?${documentSubTypeDetails[0].Category_Id}?${args.documentName}.pdf`;
            let pdfDocumentName = `${process.env.domainName}/${orgCode}/Employees Document Upload/${fileName}`;
            let res =  await copyS3Document(pdfDocumentName, documentFileName);
            console.log("The copy s3 document response:", res);

            let documentData = {};
            if(tableName === 'candidate_document_category') {
                documentData = {
                    Candidate_Id: signatureId,
                    Category_Id: documentSubTypeDetails[0].Category_Id,
                    Document_Type_Id: documentSubTypeDetails[0].Document_Type_Id,
                    Sub_Document_Type_Id: documentSubTypeDetails[0].Document_Sub_Type_Id,
                    Document_Name: documentSubTypeDetails[0].Document_Sub_Type,
                    Added_On : moment.utc().format("YYYY-MM-DD HH:mm:ss")
                }
            } else {
                documentData = {
                    Employee_Id: signatureId,
                    Document_Sub_Type_Id: documentSubTypeDetails[0].Document_Sub_Type_Id,
                    Document_Name: documentSubTypeDetails[0].Document_Sub_Type,
                    Added_On : moment.utc().format("YYYY-MM-DD HH:mm:ss")
                }
            }

            let docTableName = getPersonDetails[0]?.signatureKey === 'Candidate' ? 'candidate_documents' : 'emp_documents';
            let insertData = await organizationDbConnection(tableName).insert(documentData);
            console.log("The inserted data:", insertData);
            if (insertData && fileName) {
                await organizationDbConnection(docTableName).delete()
                .where('Document_Id', insertData[0]);

                await organizationDbConnection(docTableName)
                .insert({
                    'Document_Id': insertData[0],
                    'File_Name': fileName,
                    'File_Size': 0
                })
                console.log(`Doc-sign addDocument added ${docTableName} table`)
            }
        }
    } catch(err){
        console.error("Error while addDocument function main catch block", err)
    }
}