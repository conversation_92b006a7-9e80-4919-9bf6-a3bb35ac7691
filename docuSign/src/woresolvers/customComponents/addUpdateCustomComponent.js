// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tableAlias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
//Require validation function
const { validateCustomComponentInputs } = require('../../../common/inputValidations');
const moment = require('moment');

let organizationDbConnection;
module.exports.addUpdateCustomComponent = async (parent, args, context, info) => {
    console.log('Inside addUpdateCustomComponent function');
    let validationError = {};
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const customComponentId = args.customComponentId || 0;
        const loginEmployeeId = context.Employee_Id;
        const currentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');

        // Field validations
        const fieldValidations = {
            customComponentName: "IVE0812",
        };
        if(args.content != null){
            fieldValidations.content = "IVE0813";
        }
        validationError = await validateCustomComponentInputs(args, fieldValidations);

        // Check access rights
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection, loginEmployeeId, null, '', 'UI', false, args.formId
        );

        // Verify add/update permissions
        if (Object.keys(checkRights).length === 0 ||
            (customComponentId === 0 && checkRights.Role_Add !== 1) ||
            (customComponentId > 0 && checkRights.Role_Update !== 1)) {
            throw customComponentId > 0 ? '_DB0102' : '_DB0101';
        }

        // Check validation errors
        if(Object.keys(validationError).length > 0){
            throw 'IVE0000';
        }

        // Check for duplicate customComponentName
        // If serviceProviderId is provided, check uniqueness within that serviceProviderId
        // If serviceProviderId is null, check uniqueness where serviceProviderId is also null
        let duplicateCheckQuery = organizationDbConnection(ehrTables.customComponent)
            .count('Custom_Component_Id as componentCount')
            .where('Custom_Component_Name', args.customComponentName);

        // Checking serviceProviderId condition
        if(args.serviceProviderId != null){
            duplicateCheckQuery = duplicateCheckQuery.where('Service_Provider_Id', args.serviceProviderId);
        } else {
            duplicateCheckQuery = duplicateCheckQuery.whereNull('Service_Provider_Id');
        }

        // Exclude current record when updating
        if(customComponentId > 0){
            duplicateCheckQuery = duplicateCheckQuery.whereNot('Custom_Component_Id', customComponentId);
        }

        const duplicateResult = await duplicateCheckQuery;

        if(duplicateResult && duplicateResult[0].componentCount > 0){
            console.log('Duplicate custom component name found:', args.customComponentName);
            throw 'CHR00119';
        }

        // Check for unique default component per service provider and type
        // Only one component can be default for each service provider + type combination
        if(args.default === 'Yes'){
            let defaultCheckQuery = organizationDbConnection(ehrTables.customComponent)
                .count('Custom_Component_Id as defaultCount')
                .where('Type', args.type)
                .where('Default', 'Yes');

            // Check within the same service provider
            if(args.serviceProviderId != null){
                defaultCheckQuery = defaultCheckQuery.where('Service_Provider_Id', args.serviceProviderId);
            } else {
                defaultCheckQuery = defaultCheckQuery.whereNull('Service_Provider_Id');
            }

            // Exclude current record when updating
            if(customComponentId > 0){
                defaultCheckQuery = defaultCheckQuery.whereNot('Custom_Component_Id', customComponentId);
            }

            const defaultCheckResult = await defaultCheckQuery;

            if(defaultCheckResult && defaultCheckResult[0].defaultCount > 0){
                console.log('A default component already exists for this service provider and type:', args.serviceProviderId, args.type);
                throw 'CHR0188';
            }
        }

        // Prepare data object
        const componentData = {
            Custom_Component_Name: args.customComponentName,
            Service_Provider_Id: args.serviceProviderId,
            Type: args.type,
            Default: args.default,
            Content: args.content,
            Visibility: args.visibility,
            Custom_Group_Id: args.customGroupId,
            Status: args.status,
            Clause_Id: args.clauseId != null ? JSON.stringify(args.clauseId) : null
        };

        let result;

        // Update existing component
        if(customComponentId > 0){
            componentData.Updated_On = currentDateTime;
            componentData.Updated_By = loginEmployeeId;

            result = await organizationDbConnection(ehrTables.customComponent)
                .update(componentData)
                .where('Custom_Component_Id', customComponentId);

            if(!result){
                throw 'CHR0142';
            }

            await commonLib.func.createSystemLogActivities({
                userIp: context.User_Ip,
                employeeId: loginEmployeeId,
                organizationDbConnection: organizationDbConnection,
                message: `Custom Component ${args.customComponentName} was updated by ${loginEmployeeId}`
            });

            return { errorCode: "", message: "Custom component has been updated successfully." };
        }
        // Add new component
        else {
            componentData.Added_On = currentDateTime;
            componentData.Added_By = loginEmployeeId;

            result = await organizationDbConnection(ehrTables.customComponent)
                .insert(componentData);

            if(!result){
                throw 'CHR0143';
            }

            await commonLib.func.createSystemLogActivities({
                userIp: context.User_Ip,
                employeeId: loginEmployeeId,
                organizationDbConnection: organizationDbConnection,
                message: `Custom Component ${args.customComponentName} was added by ${loginEmployeeId}`
            });

            return { errorCode: "", message: "Custom component has been added successfully." };
        }
    }
    catch (mainCatchError) {
        console.log('Error in addUpdateCustomComponent function main catch block.', mainCatchError);

        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateCustomComponent function - ', validationError);
            throw new UserInputError(errResult.message, { validationError: validationError });
        }

        let errResult = commonLib.func.getError(mainCatchError, 'CHR0144');
        throw new ApolloError(errResult.message, errResult.code);
    }
    finally{
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
}







