//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server to return error message
const { ApolloError,UserInputError } = require('apollo-server-lambda');
const knex=require('knex')
const{formName}=require('../../common/appconstants')
let moment = require('moment-timezone');
const { ehrTables } = commonLib.tableAlias;

module.exports.addAccreditationDetails = async (parent, args, context, info) => {
    let organizationDbConnection;
    let validationError={};
    try{
        console.log("Inside addAccreditationDetails() function.")
        let logInEmpId=context.Employee_Id;
        let isAdmin=0;
        let employeeIdsArray=[];
        let accreditationDetailsToBeAdded=[]
        // get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // Check access rights exist for employee or not
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId,formName.accreditation, '', 'UI');
        if (Object.keys(checkRights).length === 0) {
            // throw error if view rights is not exists
            throw ('_DB0100');
        }
        else if(checkRights.Role_Add === 1) {
            if (checkRights.Employee_Role.toLowerCase() === 'admin') {
                isAdmin = 1;
            }
            let currentDateTime=moment.utc().format("YYYY-MM-DD HH-MM-SS");
            let receivedDate;
            let expiryDate;
            let accreditationDetails=args.accreditationDetails;
            let validationErrorAndDates;
            for(let i=0;i<accreditationDetails.length;i++)
            {
                receivedDate =accreditationDetails[i]['receivedDate']
                expiryDate = accreditationDetails[i]['expiryDate']?accreditationDetails[i]['expiryDate']:null;
                validationErrorAndDates=await commonLib.func.validateInputDateAndGetDesiredDateFormat(receivedDate,expiryDate);
                validationError=validationErrorAndDates.validationError;
                let fileExtension=(accreditationDetails[i].fileName.substr(accreditationDetails[i].fileName.lastIndexOf('.') + 1 )).toLowerCase();
                let validExtension=await commonLib.func.isValidExtension('accreditation',fileExtension);
                if(!validExtension)
                {
                    validationError['IVE0236']= commonLib.func.getError('', 'IVE0236').message;
                }
                if(!accreditationDetails[i].accreditationCategoryAndTypeId)
                {
                    validationError['IVE0258']= commonLib.func.getError('', 'IVE0258').message;
                }
                if(Object.keys(validationError).length>0)
                {
                    throw('IVE0000');
                }
                else{
                    let accreditationDetailsInJson={
                        Employee_Id:args.employeeId,
                        Accreditation_Category_And_Type_Id:accreditationDetails[i].accreditationCategoryAndTypeId,
                        File_Name:accreditationDetails[i].fileName,
                        Received_Date:validationErrorAndDates.receivedDate,
                        Expiry_Date:validationErrorAndDates.expiryDate,
                        Identifier:accreditationDetails[i].identifier?args.identifier:null,
                        Added_On:currentDateTime,
                        Added_By:logInEmpId
                    }
                    accreditationDetailsToBeAdded.push(accreditationDetailsInJson)
                }
            }
            if(!isAdmin)
            {
                let employeeIds = await commonLib.func.getEmployeeIdsOrManagerHierarchyBasedOnRole(organizationDbConnection,1,logInEmpId,0,isAdmin);
                if(employeeIds['employeeIdsArray'])
                {
                    employeeIdsArray=employeeIds['employeeIdsArray'];
                }
            }
            
            if(isAdmin || employeeIdsArray.includes(args.employeeId))
            {
                return(
                    organizationDbConnection(ehrTables.employeeAccreditationDetails)
                    .insert(accreditationDetailsToBeAdded)
                    .then(data=>{
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return {errorCode:"" ,message:"Accreditation details added successfully."}
                    })
                    .catch(e=>{
                        console.log("Error in addAccreditationDetails() .catch block",e);
                        throw('EO0110')
                    })
                )
            }
            else{
                throw('_DB0112');
            }
        }
        else{
            throw('IVE0000')
        }
    }   
    catch(e)
    {
        console.log("Error in addAccreditationDetails() main catch block",e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if(e=='IVE0000')
        {
            let errResult = commonLib.func.getError(e, 'IVE0000');
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }
        let errResult = commonLib.func.getError(e, 'EO0110');
        throw new ApolloError(errResult.message, errResult.code);
    }
}