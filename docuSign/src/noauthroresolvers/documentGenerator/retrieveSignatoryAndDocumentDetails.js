//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require constants
const { defaultValues,urlEncryption } = require('../../../common/appconstants');

//Retrieve the authorizer and document details
module.exports.retrieveSignatoryAndDocumentDetails = async (parent, args, context, info) => {
    console.log('Inside retrieveSignatoryAndDocumentDetails() function.');
    let organizationDbConnection;
    let errResult;
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Require table alias
        const { ehrTables } = commonLib.tableAlias;
        //Require cryptr package
        const cryptrObj = require('cryptr');
        //Get the encryption key from constant file
        const cryptr = new cryptrObj(urlEncryption.encryptionKey);

        if(args.authorizerDetails){
            let decryptedString = cryptr.decrypt(args.authorizerDetails);

            decryptedString = decryptedString.split('&');//[ 'authorizerId=5', 'documentId=25' ]
            let authorizerId = decryptedString[0].split('=');//[ 'authorizerId', '5' ]
            let documentId = decryptedString[1].split('=');//[ 'documentId', '25' ]
            authorizerId = authorizerId[1];
            documentId = documentId[1];

            if(authorizerId && documentId){
                //Get the generated document details for the document id
                return(
                organizationDbConnection(ehrTables.empGeneratedDocuments + ' as EGD')
                .select('EGD.Document_Content as documentContent','EGD.Generated_Document_Id as generatedDocumentId','EGD.Form_Id as formId',
                'EGD.Status as documentStatus', 'DTE.Document_Attachment as documentAttachment', 'DTE.Document_Subtype_Id as documentSubTypeId',
                'GDC.Component_Inputs as authorizedSignatories','EGD.Document_Name as documentName','EGD.Document_Link as documentLink')
                .leftJoin(ehrTables.generatedDocumentCustomComponents + ' as GDC',function() {
                    this.on('EGD.Generated_Document_Id', '=', 'GDC.Generated_Document_Id')
                        .on('GDC.Component_Name', '=', organizationDbConnection.raw('?', [defaultValues.authorizedSignatoryComponentName]))
                })
                .leftJoin(ehrTables.documentTemplateEngine + ' as DTE','DTE.Document_Template_Id','EGD.Template_Id')
                .where('EGD.Generated_Document_Id',documentId)
                .then(async(documentDetails) => {
                    if(documentDetails && documentDetails.length > 0){
                            if(documentDetails[0].documentStatus === 'Declined'){
                                console.log('Current authorizer already declined the document.',documentDetails[0]);
                                throw 'CDG0173';
                            }

                        let authorizerDetails = documentDetails[0].authorizedSignatories;
                        //Check the authorizer details exist
                        if(authorizerDetails){
                            authorizerDetails = JSON.parse(documentDetails[0].authorizedSignatories);
                            if(authorizerDetails.length > 0){
                                //Filter the current authorizer details using the authorizer id
                                let currentAuthorizerDetails = authorizerDetails.filter(item=>item.signatureEmployeeId==authorizerId);
                                if(currentAuthorizerDetails && currentAuthorizerDetails.length > 0){
                                    //Check the authorizer signed or not
                                    if(currentAuthorizerDetails[0].status === 'Not Signed'){
                                        let authorizerAndDocumentDetails ={
                                            generatedDocumentId:documentDetails[0].generatedDocumentId,
                                            documentAttachment:documentDetails[0].documentAttachment,
                                            documentSubTypeId:documentDetails[0].documentSubTypeId,
                                            documentName:documentDetails[0].documentName,
                                            documentContent:documentDetails[0].documentContent,
                                            documentLink:documentDetails[0].documentLink,
                                            authorizerDetails:currentAuthorizerDetails[0],
                                            signatureFileName: ''
                                        };
                                        //Find the field name
                                        let fieldName = '';
                                        if(currentAuthorizerDetails[0].signatureKey === 'candidate'){
                                            fieldName = 'Candidate_Id';
                                        }else{
                                            fieldName = 'Employee_Id';
                                        }

                                        //Get the authorizer signature file name from the table
                                        return (
                                        organizationDbConnection('signature_details')
                                        .select('File_Name')
                                        .where(fieldName,currentAuthorizerDetails[0].signatureEmployeeId)
                                        .then(signatureDetails=>{
                                            authorizerAndDocumentDetails.signatureFileName = (signatureDetails && signatureDetails.length > 0) ? signatureDetails[0].File_Name : '';

                                            //Destroy DB connection
                                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                                            return {errorCode:'',message:'Signature and the document details retrieved successfully.',authorizerAndDocumentDetails:authorizerAndDocumentDetails};
                                        })
                                        )
                                    }
                                    
                                    else{
                                        console.log('Current authorizer already signed the document.',currentAuthorizerDetails);
                                        throw ('CDG0112');
                                    }
                                }else{
                                    console.log('Current authorizer details not exist.',currentAuthorizerDetails);
                                    throw ('CDG0114');
                                }
                            }else{
                                console.log('Parsed authorizer details not exist.',authorizerDetails);
                                throw ('CDG0111');
                            }
                        }else{
                            console.log('Authorizer details not exist.',authorizerDetails);
                            throw ('CDG0111');
                        }
                    }else{
                        console.log('Generated document not exist.',documentId);
                        throw('_EC0001');
                    }
                })
                .catch(catchError =>{
                    console.log('Error in the retrieveSignatoryAndDocumentDetails() function .catch block. ',catchError);
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    errResult = commonLib.func.getError(catchError, 'CDG0113');
                    //Return error response
                    throw new ApolloError(errResult.message,errResult.code);
                })
                )
            }else{
                console.log('Authorizer id or document id does not exist.',decryptedString);
                throw('_EC0007');
            }
        }else{
            console.log('Input does not exist.',args);
            throw('_EC0007');
        }

    }catch(mainCatchErr) {
        console.log('Error in the retrieveSignatoryAndDocumentDetails() function main catch block. ',mainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchErr, 'CDG0012');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    }
};