# defining custom data type
scalar Date

type Query{
  retrieveSignatoryAndDocumentDetails(authorizerDetails:String!):retrieveSignatoryAndDocumentDetailsResponse!
}

type retrieveSignatoryAndDocumentDetailsResponse{
    errorCode:String
    message:String
    authorizerAndDocumentDetails:authorizerAndDocumentDetails
}

type authorizerAndDocumentDetails{
  generatedDocumentId:Int
  documentName:String
  documentContent:String
  documentAttachment: Int
  documentSubTypeId: Int
  documentLink:String
  authorizerDetails:currentAuthorizerDetails
  signatureFileName:String
}

type currentAuthorizerDetails{
  signatureLevelId:Int
  signatureKey:String
  signatureEmployeeId:Int
  signatureEmployeeName:String
  status:String
}

type commonResponse{
  errorCode: String,
  message: String
}

schema {
  query: Query
}