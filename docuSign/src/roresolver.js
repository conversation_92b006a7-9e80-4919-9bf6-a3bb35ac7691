const listDocumentTemplate = require('./roresolvers/documentGenerator/listDocumentTemplate');
const listEmployeeGeneratedDocuments = require('./roresolvers/documentGenerator/listEmployeeGeneratedDocuments');
const listTemplatesInDropdown = require('./roresolvers/documentGenerator/listTemplatesInDropdown');
const listTemplateCustomComponents = require('./roresolvers/documentGenerator/listTemplateCustomComponents');
const listDocumentTemplateFields = require('./roresolvers/documentGenerator/listDocumentTemplateFields');
const retrieveDocumentInputs = require('./roresolvers/documentGenerator/retrieveDocumentInputs');
const retrieveDocumentDetails = require('./roresolvers/documentGenerator/retrieveDocumentDetails');
const listEmployeesInGenerator = require('./roresolvers/documentGenerator/listEmployeesInGenerator');
const sendDocumentInEmail = require('./roresolvers/documentGenerator/sendDocumentInEmail');
const listCandidates = require('./roresolvers/documentGenerator/listCandidates');
const resendEmailToSignatory = require('./roresolvers/documentGenerator/resendEmailToSignatory');
const retrieveAccreditationDetails = require('./roresolvers/retrieveAccreditationDetails')
const listCustomComponents = require('./roresolvers/customComponents/listCustomComponents')
const listClauseComponents = require('./roresolvers/clauseComponents/listClauseComponents')
const returnResolvedPlaceholderContent = require('./roresolvers/documentGenerator/returnResolvedPlaceholderContent')

// Define resolver
const resolvers = {
    Query: Object.assign({},
        listDocumentTemplate,
        listEmployeeGeneratedDocuments,
        listTemplatesInDropdown,
        listTemplateCustomComponents,
        listDocumentTemplateFields,
        retrieveDocumentInputs,
        retrieveDocumentDetails,
        listEmployeesInGenerator,
        sendDocumentInEmail,
        listCandidates,
        resendEmailToSignatory,
        retrieveAccreditationDetails,
        listCustomComponents,
        listClauseComponents,
        returnResolvedPlaceholderContent
    )
}
exports.resolvers = resolvers;
