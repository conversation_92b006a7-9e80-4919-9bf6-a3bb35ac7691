const { ApolloServer, gql } = require('apollo-server-lambda');
const { resolvers } = require('./noauthroresolver');
const path = require("path");
const fs = require('fs');
const typeDefs = gql(fs.readFileSync(path.resolve()+'/src/'+'noauthroschema.graphql', 'utf8'));
// require common hrapp-corelib functions
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');

module.exports.graphql = (event, context, callback) => {
    context.callbackWaitsForEmptyEventLoop = false; //to send the response immediately when callback executes

    // Create object for ApolloServer
    const server = new ApolloServer({
        typeDefs,
        resolvers,
        context: async ({ event }) => {
            console.log('Inside the docusign noauthrohandler.js');

            // get the orgCode from http headers
            let Org_Code = await commonLib.func.getOrgCode(event);
            console.log('OrgCode',Org_Code);

            // Append User_Ip in context
            let User_Ip = event.headers.user_ip; 

            // get database connection object ( pass stageName,dbPrefix,dbSecretName, region and Org_Code as params )
            let connection = Org_Code ? await commonLib.func.getDataBaseConnection(
            {
                stageName: process.env.stageName, dbPrefix: process.env.dbPrefix, dbSecretName: process.env.dbSecretName,
                region: process.env.region, orgCode: Org_Code
            }) : null;

            // for orgDbConnection
            let orgDbConnection = knex(connection.OrganizationDb);
            
            // close DB connection
            orgDbConnection.destroy();

            // return header to resolver function
            // skipAuth flag indicates this is an unauthenticated request - resolvers should skip access rights check
            return { Org_Code, connection, User_Ip, skipAuth: true };
        }
    });
    const handler = server.createHandler({
        cors: {
            method: 'POST',
            allowHeaders: '*'
        }
    });
    
    function callbackFilter(error, output) {
        output.headers['Access-Control-Allow-Origin'] = '*';
        output.headers['Access-Control-Allow-Credentials'] = true;
        callback(error, output);
    }
    return handler(event, context, callbackFilter);
};

