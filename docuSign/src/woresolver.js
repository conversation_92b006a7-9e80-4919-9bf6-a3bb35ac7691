const listDocumentTemplate = require('./roresolvers/documentGenerator/listDocumentTemplate');
const addUpdateDocumentTemplate=require('./woresolvers/documentGenerator/addUpdateDocumentTemplate');
const deleteDocumentTemplate=require('./woresolvers/documentGenerator/deleteDocumentTemplate');
const deleteEmployeeGeneratedDocuments=require('./woresolvers/documentGenerator/deleteEmployeeGeneratedDocuments');
const addUpdateEmployeeGeneratedDocument = require('./woresolvers/documentGenerator/addUpdateEmployeeGeneratedDocument')
const addAccreditationDetails = require('./woresolvers/addAccreditationDetails')
const addUpdateCustomComponent = require('./woresolvers/customComponents/addUpdateCustomComponent')
// Define resolver
const resolvers = {
    Query: Object.assign({},
        listDocumentTemplate
    ),
    Mutation: Object.assign({},
        addUpdateDocumentTemplate,
        deleteDocumentTemplate,
        deleteEmployeeGeneratedDocuments,
        addUpdateEmployeeGeneratedDocument,
        addAccreditationDetails,
        addUpdateCustomComponent
    )
}
exports.resolvers = resolvers;