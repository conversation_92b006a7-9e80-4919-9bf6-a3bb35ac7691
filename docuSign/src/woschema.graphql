# defining custom data type
scalar Date

type Query {
  listDocumentTemplate: listDocumentTemplateResponse!
}

type Mutation {
  addUpdateDocumentTemplate(
    documentTemplateId: Int!
    title: String!
    templateContent: String!
    signatoryKeys: [String]!
    registeredBusinessAddress: Int!
    formId: Int
    category: String
    reportId: [Int]
    documentAttachment: Int
    generateDocForOnboarding: Int
    documentSubTypeId: Int
    description: String
    pageScope: String
    templateHeader: String
    templateFooter: String
    customFooterId: Int
    customHeaderId: Int
  ): commonResponse!
  deleteDocumentTemplate(documentTemplateId: Int!): commonResponse!
  deleteEmployeeGeneratedDocuments(generatedDocumentId: Int!): commonResponse!
  addUpdateEmployeeGeneratedDocument(
    generatedDocumentId: Int!
    employeeId: Int!
    candidateId: Int!
    templateId: Int
    templateName: String
    documentAttachment: Int
    documentSubTypeId: Int
    documentName: String!
    registeredBusinessAddress: String!
    documentContent: String!
    documentHeader: String
    documentFooter: String
    pageScope: String
    customComponents: [customComponentsInput]
    signingAuthorities: String
    additionalDetails:String
    status: String!
  ): addUpdateEmployeeGeneratedDocumentResponse!
  addAccreditationDetails(
    employeeId: Int!
    accreditationDetails: [accreditationDetails!]!
  ): commonResponse!

  addUpdateCustomComponent(
    formId: Int!
    customComponentId: Int!
    customComponentName: String!
    serviceProviderId: Int
    type: String!
    default: String!
    content: String!
    visibility: String!
    customGroupId: Int
    status: String!
    clauseId: [Int]
  ): commonResponse!
}

type commonResponse {
  errorCode: String
  message: String
}

type addUpdateEmployeeGeneratedDocumentResponse{
  errorCode: String,
  message: String,
  documentLink: String,
  generatedDocumentId: Int
}

input customComponentsInput {
  componentName: String!
  componentInputs: String!
}

type listDocumentTemplateResponse {
  errorCode: String
  message: String
  documentTemplateDetails: [documentTemplateDetails]
}

type documentTemplateDetails {
  documentTemplateId: Int
  title: String
  registeredBusinessAddress: Int
  templateContent: String
  formId: Int
  formName: String
}

input accreditationDetails {
  accreditationCategoryAndTypeId: Int!
  fileName: String!
  receivedDate: String!
  expiryDate: String
  identifier: String
}

schema {
  query: Query
  mutation: Mutation
}
