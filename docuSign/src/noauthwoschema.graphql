# defining custom data type
scalar Date

type Query{
  retrieveSignatoryAndDocumentDetails(authorizerDetails:String!):retrieveSignatoryAndDocumentDetailsResponse!
}

type Mutation {
  updateFileName(documentId:Int!,fileName:String!,candidateId:Int!,employeeId:Int!,saveSignature:Int!):commonResponse!
  updateDocumentDetails(authorizerDetails:authorizerDetailsInput!,documentId:Int!):updateDocumentDetailsResponse!
  updateOfferLetterStatus(documentId:Int! ,status: String!,candidateId: Int, signatureKey:String): commonResponse!
  uploadEmployeeGeneratedDocument (
    authorizerDetails:[authorizerDetailsInput]!,
    documentId:Int!,
    documentName:String!,
    signatureFileName:String!,
    signatoryEmployeeId: Int!,
    signatoryKey:String!,
    pdfDocumentName:String!,
    documentAttachment: Int,
    documentSubTypeId: Int,
    signedCount:Int!
  ):commonResponse!
  addUpdateEmployeeGeneratedDocument(
    generatedDocumentId: Int!
    employeeId: Int!
    candidateId: Int!
    templateId: Int
    templateName: String
    documentAttachment: Int
    documentSubTypeId: Int
    documentName: String!
    registeredBusinessAddress: String!
    documentContent: String!
    documentHeader: String
    documentFooter: String
    customComponents: [customComponentsInput]
    signingAuthorities: String
    additionalDetails:String
    status: String!
  ): addUpdateEmployeeGeneratedDocumentResponse!
}

type retrieveSignatoryAndDocumentDetailsResponse{
    errorCode:String
    message:String
    authorizerAndDocumentDetails:authorizerAndDocumentDetails
}

type authorizerAndDocumentDetails{
  generatedDocumentId:Int
  documentName:String
  documentContent:String
  documentLink:String
  authorizerDetails:currentAuthorizerDetails
  signatureFileName:String
}

type currentAuthorizerDetails{
  signatureLevelId:Int
  signatureKey:String
  signatureEmployeeId:Int
  signatureEmployeeName:String
  status:String
}

input authorizerDetailsInput{
  signatureLevelId:Int
  signatureKey:String
  signatureEmployeeId:Int
  signatureEmployeeName:String
  status:String
}

input customComponentsInput {
  componentName: String!
  componentInputs: String!
}

type commonResponse{
  errorCode: String,
  message: String
}

type addUpdateEmployeeGeneratedDocumentResponse{
  errorCode: String,
  message: String,
  documentLink: String,
  generatedDocumentId: Int
}

type updateDocumentDetailsResponse{
  errorCode: String,
  message: String,
  documentDetails: String
}

schema {
  query: Query
  mutation:Mutation
}