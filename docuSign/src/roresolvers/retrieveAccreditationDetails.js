//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const knex = require('knex')
const { formName } = require('../../common/appconstants');
const { fetchJobRoleDetails } = require('../../common/commonFunctions');

const { ehrTables } = commonLib.tableAlias;

module.exports.retrieveAccreditationDetails = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        console.log("Inside retrieveAccreditationDetails() function.")
        let logInEmpId = context.Employee_Id;
        let isAdmin = 0;
        let isServiceProviderAdmin = 0;
        let isManager = 0;
        let getServiceProviderEmployees
        // get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // Check report view access rights exist for employee or not
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId, formName.accreditation, '', 'UI');
        if (Object.keys(checkRights).length === 0) {
            // throw error if view rights is not exists
            throw ('_DB0100');
        }
        else if (checkRights.Role_View === 1) {
            // check loggedIn employee is  admin or not
            if (checkRights.Employee_Role.toLowerCase() === 'admin') {
                let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId, formName.serviceProviderAdmin, '', 'UI');
                if (checkRights.Role_Update === 1) {
                    getServiceProviderEmployees = await commonLib.func.getServiceProviderEmployees(organizationDbConnection, logInEmpId)
                    getServiceProviderEmployees = getServiceProviderEmployees.map(row => row.Employee_Id);
                    var employeeIdsArray = getServiceProviderEmployees
                    isServiceProviderAdmin = 1
                } else {
                    isAdmin = 1;
                }
            } else if (checkRights.Is_Manager === 1) {
                isManager = 1
            }
            if (isServiceProviderAdmin || isAdmin || isManager) {
                if (isManager) {
                    // get the employee based on manager
                    var { employeeIdsArray } = await commonLib.func.getEmployeeIdsOrManagerHierarchyBasedOnRole(organizationDbConnection, 1, logInEmpId, 0, isAdmin);
                }
                return (
                    organizationDbConnection(ehrTables.employeeAccreditationDetails)
                        .select('EAD.Accreditation_Detail_Id as accreditationDetailId', 'EAD.Employee_Id as employeeId', 'EAD.Accreditation_Category_And_Type_Id as accreditationCategoryAndTypeId', 'EAD.File_Name as fileName', 'EAD.Received_Date as receivedDate', 'EAD.Expiry_Date as expiryDate', 'EAD.Identifier as identifier', 'EAD.Verified as verified', 'EAD.Verified_Date as verifiedDate',
                            'ACAT.Accreditation_Category as accreditationCategory', 'ACAT.Accreditation_Type as accreditationType', 'SP.Service_Provider_Name as serviceProviderName',
                            'EJ.Designation_Id as designationId', 'EJ.Department_Id as departmentId', 'EJ.EmpType_Id as employeeTypeId', 'ET.Employee_Type as employeeType', 'EJ.Location_Id as locationId', 'EJ.Work_Schedule as workScheduleId', 'EJ.Emp_Status as employeeStatus',
                            organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as employeeName"),
                            organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as managerName"),
                            'EJ.Job_Role_Ids',
                        )
                        .from(ehrTables.employeeAccreditationDetails + ' as EAD')
                        .leftJoin(ehrTables.accreditationCategoryAndType + ' as ACAT', 'ACAT.Accreditation_Category_And_Type_Id', 'EAD.Accreditation_Category_And_Type_Id')
                        .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'EPI.Employee_Id', 'EAD.Employee_Id')
                        .leftJoin(ehrTables.empJob + ' as EJ', 'EPI.Employee_Id', 'EJ.Employee_Id')
                        .leftJoin(ehrTables.employeeType + ' as ET', 'ET.EmpType_Id', 'EJ.EmpType_Id')
                        .leftJoin(ehrTables.empPersonalInfo + ' as EPI1', 'EJ.Manager_Id', 'EPI1.Employee_Id')
                        .leftJoin(ehrTables.serviceProvider + ' as SP', 'SP.Service_Provider_Id', 'EJ.Service_Provider_Id')
                        .where(function () {
                            if (employeeIdsArray && employeeIdsArray.length) {
                                this.whereIn('EAD.Employee_Id', employeeIdsArray)
                            }
                        })
                        .then(async data => {
                            data = await fetchJobRoleDetails(data, organizationDbConnection);
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: '', message: 'Employee accreditation details retrieved successfully.', accreditationDetails: data };
                        })
                        .catch(e => {
                            console.log("Error in retrieveAccreditationDetails() function .catch block", e);
                            throw ("EO0105")
                        })
                )
            } else {
                throw '_DB0100';
            }

        }
        else {
            throw '_DB0100';
        }
    }
    catch (e) {
        console.log("Error in retrieveAccreditationDetails() function main catch block", e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(e, 'EO0105');
        throw new ApolloError(errResult.message, errResult.code);
    }
    
}