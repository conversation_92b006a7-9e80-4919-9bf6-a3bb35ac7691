//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require constants
const { formIds } = require('../../../common/appconstants');

//List the document template in the dropdown
module.exports.listTemplatesInDropdown = async (parent, args, context, info) => {
    console.log('Inside listTemplatesInDropdown function.');
    let organizationDbConnection;
    let errResult;
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Require table alias
        const { ehrTables } = commonLib.tableAlias;
        //Retrieve the template from the document template engine table
        return(
            organizationDbConnection(ehrTables.documentTemplateEngine + ' as DTE')
            .select('DTE.Document_Template_Id as documentTemplateId','DTE.Title as title',
                'DTE.Template_Content as templateContent', 'DTE.Document_Attachment as documentAttachment',
                'DTE.Page_Scope as pageScope', 'DTE.Template_Header as templateHeader', 'DTE.Template_Footer as templateFooter',
                'DTE.Document_Subtype_Id as documentSubTypeId', 'DTE.Registered_Business_Address as registeredBusinessAddress','DTE.Template_Fields as templateFields',
                'FD.Form_Id as formId','FD.Form_Name as formName', 'DTE.Report_Id as reportId',
                organizationDbConnection.raw("CASE WHEN DTE.Form_Id = 16 THEN 'Yes' ELSE 'No' END AS candidateTemplate"),
                organizationDbConnection.raw("CASE WHEN DTE.Form_Id = 243 THEN 'Yes' ELSE 'No' END AS employeeTemplate")
            )
            .leftJoin(ehrTables.ehrForms+' as FD','FD.Form_Id','DTE.Form_Id')
            .modify(function(queryBuilder){
                if(args.templateFormId && args.templateFormId.length > 0){
                    queryBuilder.whereIn('DTE.Form_Id', args.templateFormId);
                }else{
                    queryBuilder.whereNull('DTE.Form_Id').orWhereNotIn('DTE.Form_Id', [formIds.reportDefinition]);
                }
            })
            .then(async(documentTemplateDetails)=>{
                if(documentTemplateDetails && documentTemplateDetails.length > 0){
                    for (let i in documentTemplateDetails){
                        let templateFields = documentTemplateDetails[i].templateFields ? JSON.parse(documentTemplateDetails[i].templateFields) : [];
                        let entity = [];
                        if(templateFields && templateFields.length > 0){
                            templateFields = templateFields.map(f => {
                                const match = f.match(/\["(.+?)"\]/);
                                return match ? match[1] : f;
                            }).filter(Boolean);
                            entity = await organizationDbConnection(ehrTables.documentTemplateFields)
                                        .pluck('Entity')
                                        .whereIn('Field_Name',templateFields)
                                        .groupBy('Entity')
                                        .then();
                            entity= (entity && entity.length > 0) ? entity : []
                        }
                        documentTemplateDetails[i].entity = entity;
                    }

                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    //Return success response
                    return { errorCode:'',message:'Document template details retrieved successfully.',documentTemplateDetails:(documentTemplateDetails.length>0)?documentTemplateDetails:[]};
                }else{
                    return { errorCode:'', message:'Document template does not exists.', documentTemplateDetails:[]}
                }
            })
            .catch(catchError => {
                console.log('Error in listTemplatesInDropdown function .catch block',catchError);
                errResult = commonLib.func.getError(catchError, 'CDG0107');
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                //Return error response
                throw new ApolloError(errResult.message,errResult.code);
            })
        );
    }catch(listDocumentTemplateMainCatchErr) {
        console.log('Error in the listTemplatesInDropdown function main catch block. ',listDocumentTemplateMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(listDocumentTemplateMainCatchErr, 'CDG0008');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    }
};