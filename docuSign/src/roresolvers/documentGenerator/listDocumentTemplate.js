//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require constants
const { formName } = require('../../../common/appconstants');

//List the document template
module.exports.listDocumentTemplate = async (parent, args, context, info) => {
    console.log('Inside listDocumentTemplate function.');
    let organizationDbConnection;
    let errResult;
    try{
        //Require table alias
        const { ehrTables } = commonLib.tableAlias;

        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Get the login employee - document generator form access
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id , formName.docuSign, '', 'UI');
        //Check view rights exist or not                 
        if(Object.keys(checkRights).length>0 && checkRights.Role_View === 1) {
            //Retrieve the template from the document template engine table
            return(
                organizationDbConnection(ehrTables.documentTemplateEngine + ' as DTE')
                .select('DTE.Document_Template_Id as documentTemplateId','DTE.Title as title','DTE.Registered_Business_Address as registeredBusinessAddress',
                'DTE.Template_Content as templateContent','FD.Form_Name as formName','DTE.Form_Id as formId', 'DTE.Category as category',
                'DTE.Report_Id as reportId', 'DTE.Document_Attachment as documentAttachment', 'DTE.Generate_Doc_For_Onboarding as generateDocForOnboarding',
                'DTE.Document_Subtype_Id as documentSubTypeId', 'DTE.Description as description', 'DTE.Page_Scope as pageScope',
                'DTE.Template_Header as templateHeader', 'DTE.Template_Footer as templateFooter',
                'DTE.Custom_Footer_Id as customFooterId', 'DTE.Custom_Header_Id as customHeaderId')
                .leftJoin(ehrTables.ehrForms+' as FD','FD.Form_Id','DTE.Form_Id')
                .then((documentTemplateDetails)=>{
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    //Return success response
                    return { errorCode:'',message:'Document template details retrieved successfully.',documentTemplateDetails:(documentTemplateDetails.length>0)?documentTemplateDetails:[]};
                })
                .catch(catchError => {
                    console.log('Error in listDocumentTemplate function catch block',catchError);
                    errResult = commonLib.func.getError(catchError, 'CDG0101');
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    //Return error response
                    throw new ApolloError(errResult.message,errResult.code);
                })
            );
        }else{
            console.log('Login employee id does not have view access to document generator form.');
            throw ('_DB0100');
        }
    }catch(listDocumentTemplateMainCatchErr) {
        console.log('Error in the listDocumentTemplate function main catch block. ',listDocumentTemplateMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(listDocumentTemplateMainCatchErr, 'CDG0001');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    }
};