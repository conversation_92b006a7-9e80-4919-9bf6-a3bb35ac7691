//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require common functions
const {groupTemplateFieldsByTitle,groupTemplateFieldsByEntity} = require('../../../common/documentGeneratorCommonFunctions');

//List the document template fields
module.exports.listDocumentTemplateFields = async (parent, args, context, info) => {
    console.log('Inside listDocumentTemplateFields() function.');
    let organizationDbConnection;
    let errResult;
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Require table alias
        const { ehrTables } = commonLib.tableAlias;
        //Retrieve the template fields
        return(
        organizationDbConnection(ehrTables.documentTemplateFields+' as DTF')
        .select('DTF.Entity as entity','DTF.Group_Title as groupTitle','DTF.Field_Name as fieldName')
        .then(async (templateFieldsResult)=>{
            if(templateFieldsResult.length > 0){
                let templateEntityResult = await groupTemplateFieldsByEntity(templateFieldsResult);
                
                let templateFields={};
                if(Object.keys(templateEntityResult).length > 0){
                    for (let i in templateEntityResult) {
                        //Group the response by using the group title. Sample response: {'Employee Personal Info': [ 'Employee Name' ]}
                        let tempTemplateFields = await groupTemplateFieldsByTitle(templateEntityResult[i]);
                        templateFields[templateEntityResult[i][0].entity] = tempTemplateFields;
                    }
                }
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return { errorCode:'',message:'Document template fields retrieved successfully.',documentTemplateFieldDetails: (Object.keys(templateFields).length>0) ? JSON.stringify(templateFields) : ''};
            }else{
                console.log('Document template fields does not exist.',templateFieldsResult)
                throw '_EC0001';
            }
        })
        .catch(catchError => {
            console.log('Error in listDocumentTemplateFields() function .catch block',catchError);
            errResult = commonLib.func.getError(catchError, 'CDG0109');
            //Destroy DB connection
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            //Return error response
            throw new ApolloError(errResult.message,errResult.code);
        })
        );
    }catch(listMainCatchErr) {
        console.log('Error in the listDocumentTemplateFields() function main catch block. ',listMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(listMainCatchErr, 'CDG0010');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    }
};