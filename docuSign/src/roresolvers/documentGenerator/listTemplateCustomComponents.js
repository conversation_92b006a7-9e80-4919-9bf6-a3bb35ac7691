//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require common functions
const {groupTemplateFieldsByTitle} = require('../../../common/documentGeneratorCommonFunctions');

//List the template custom components
module.exports.listTemplateCustomComponents = async (parent, args, context, info) => {
    console.log('Inside listTemplateCustomComponents() function.');
    let organizationDbConnection;
    let errResult;
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Require table alias
        const { ehrTables } = commonLib.tableAlias;
        if(args.documentTemplateId <= 0){
            throw '_EC0007';
        }else{
            let documentTemplateId = args.documentTemplateId;
            //Retrieve the template from the document template engine table
            return(
            organizationDbConnection(ehrTables.documentTemplateEngine)
            .select(ehrTables.templateFields+' as templateFields')
            .where('Document_Template_Id',documentTemplateId)
            .then((documentTemplateDetails)=>{
                if(documentTemplateDetails.length > 0){
                    let templateFields = documentTemplateDetails[0].templateFields ? JSON.parse(documentTemplateDetails[0].templateFields) : [];
                    let extractedFields = Array.isArray(templateFields)
                        ? templateFields
                            .map(item => {
                                if (typeof item !== "string") return null;
                                const match = item.match(/\["(.+?)"\]/);
                                return match ? match[1] : item;
                            })
                            .filter(Boolean)
                        : [];

                    if(templateFields && templateFields.length > 0){
                        //Retrieve the template custom components from the document template engine table
                        return(
                        organizationDbConnection(ehrTables.documentTemplateFields+' as DTF')
                        .select('DTF.Group_Title as groupTitle','DTF.Field_Name as fieldName')
                        .whereIn('DTF.Field_Name',extractedFields)
                        .where('DTF.Is_Custom_Field',1)
                        .where('DTF.Custom_Field_User_Input',1)
                        .then(async(templateCustomComponentsResult)=>{
                            //Group the response by using the group title. Sample response: {'Employee Personal Info': [ 'Employee Name' ]}
                            let templateCustomComponents = await groupTemplateFieldsByTitle(templateCustomComponentsResult);
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode:'',message:'Template custom components retrieved successfully.',templateCustomComponents:(Object.keys(templateCustomComponents).length>0) ?JSON.stringify(templateCustomComponents) : ''};
                        }));
                    }else{
                        //Destroy DB connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        //Return success response
                        return { errorCode:'',message:'Template custom components does not exists.',templateCustomComponents: ''};
                    }
                }else{
                    console.log('Document template details does not exist for the document template id.',documentTemplateId,documentTemplateDetails)
                    throw '_EC0001';
                }
            })
            .catch(catchError => {
                console.log('Error in listTemplateCustomComponents() function .catch block',catchError);
                errResult = commonLib.func.getError(catchError, 'CDG0108');
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                //Return error response
                throw new ApolloError(errResult.message,errResult.code);
            })
            );
        }
    }catch(listMainCatchErr) {
        console.log('Error in the listTemplateCustomComponents() function main catch block. ',listMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(listMainCatchErr, 'CDG0009');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    }
};