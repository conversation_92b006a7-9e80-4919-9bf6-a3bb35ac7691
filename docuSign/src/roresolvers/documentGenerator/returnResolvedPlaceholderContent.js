//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require constants

/**
 * Generates an HTML table from array of arrays data
 * First row is treated as headers, remaining rows are data
 * @param {Array<Array>} tableData - Array of arrays where first row is headers
 * @returns {string} - HTML table string
 */
/**
 * Escapes HTML special characters to prevent XSS
 * @param {*} value - Value to escape
 * @returns {string} - Escaped string
 */
const escapeHtml = (value) => {
    if (value === null || value === undefined) {
        return '';
    }
    return String(value)
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
};


const generateHtmlTable = (tableData) => {
    if (!Array.isArray(tableData) || tableData.length === 0) {
        return '';
    }

    let tableHtml = '<table style="border-collapse: collapse; width: 100%;">';

    // Generate header row from first array
    const headers = tableData[0];
    if (Array.isArray(headers) && headers.length > 0) {
        tableHtml += '<thead><tr>';
        headers.forEach(header => {
            tableHtml += `<th style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;">${escapeHtml(header)}</th>`;
        });
        tableHtml += '</tr></thead>';
    }

    // Generate data rows from remaining arrays
    if (tableData.length > 1) {
        tableHtml += '<tbody>';
        for (let i = 1; i < tableData.length; i++) {
            const row = tableData[i];
            if (Array.isArray(row)) {
                tableHtml += '<tr>';
                row.forEach(cell => {
                    tableHtml += `<td style="border: 1px solid #ddd; padding: 8px;">${escapeHtml(cell)}</td>`;
                });
                tableHtml += '</tr>';
            }
        }
        tableHtml += '</tbody>';
    }

    tableHtml += '</table>';
    return tableHtml;
};

/**
 * Gets nested value from an object using dot notation
 * @param {object} obj - The object to traverse
 * @param {string} path - Dot-separated path (e.g., "Table_Data.Report_Data")
 * @returns {*} - The value at the path or undefined
 */
const getNestedValue = (obj, path) => {
    if (!obj || !path) {
        return undefined;
    }

    const keys = path.split('.');
    let current = obj;

    for (const key of keys) {
        if (current === null || current === undefined || !current.hasOwnProperty(key)) {
            return undefined;
        }
        current = current[key];
    }

    return current;
};

/**
 * Resolves placeholders in document content with actual values from report details
 * Placeholder format: {Key["field_name"]} where Key maps directly to a key in reportDetails
 * Examples:
 *   {CandidateSalary["Annual_Ctc"]} -> reportDetails.CandidateSalary.Annual_Ctc
 *   {Personal["pPincode"]} -> reportDetails.Personal.pPincode
 *   {BankSalaryStatement["field"]} -> reportDetails.BankSalaryStatement.field
 *   {CandidateEducation["Table_Data.Report_Data"]} -> HTML table from reportDetails.CandidateEducation.Table_Data.Report_Data
 * @param {string} content - HTML content with placeholders (can be documentContent, documentHeader, or documentFooter)
 * @param {object} reportDetails - Object containing candidate data
 * @returns {string} - Content with resolved placeholders
 */
const resolvePlaceholders = (content, reportDetails) => {
    if (!content || !reportDetails) {
        return content || '';
    }

    // Regex to match placeholders like {Key["field_name"]} or {Key['field_name']}
    // Also handles escaped quotes like {Key[\"field_name\"]}
    // Key can be any alphanumeric string (e.g., CandidateSalary, Personal, BankSalaryStatement, EmployeeEducation, etc.)
    // Field name can contain dots for nested access (e.g., "Table_Data.Report_Data")
    const placeholderRegex = /\{(\w+)\[\\?["']([^"'\]]+)\\?["']\]\}/g;

    const resolvedContent = content.replace(placeholderRegex, (match, key, fieldName) => {
        try {
            // Check if the key exists in reportDetails
            if (reportDetails.hasOwnProperty(key)) {
                const categoryData = reportDetails[key];

                let value;

                // Check if fieldName contains a dot (nested property access)
                if (fieldName.includes('.')) {
                    value = getNestedValue(categoryData, fieldName);
                } else if (categoryData && categoryData.hasOwnProperty(fieldName)) {
                    value = categoryData[fieldName];
                }

                // Handle null/undefined values
                if (value === null || value === undefined) {
                    return '';
                }

                // Check if value is an array of arrays (table data)
                if (Array.isArray(value) && value.length > 0 && Array.isArray(value[0])) {
                    return generateHtmlTable(value);
                }

                // Return the value as string
                return escapeHtml(value);
            }

            // If key or field not found, return empty string
            console.log(`Placeholder not resolved: ${match} - Key: ${key}, Field: ${fieldName}`);
            return '';
        } catch (error) {
            console.log(`Error resolving placeholder ${match}:`, error);
            return '';
        }
    });

    return resolvedContent;
};

//List the document template
module.exports.returnResolvedPlaceholderContent = async (parent, args, context, info) => {
    console.log('Inside returnResolvedPlaceholderContent function.');
    let organizationDbConnection;
    let errResult;
    try{
        //Require table alias
        const { ehrTables } = commonLib.tableAlias;
        const formId = args.formId;

        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Check if this is an unauthenticated request (skipAuth flag set by noauth handler)
        const skipAuth = context.skipAuth === true;
        let hasAccess = false;

        if (skipAuth) {
            // Unauthenticated request - skip access rights check
            console.log('Unauthenticated request (skipAuth=true) - skipping access rights check');
            hasAccess = true;
        } else {
            // Authenticated request - check employee access rights
            const checkRights = await commonLib.func.checkEmployeeAccessRights(
                organizationDbConnection, context.Employee_Id, null, '', 'UI', false, formId
            );
            //Check view rights exist or not
            hasAccess = Object.keys(checkRights).length>0 && checkRights.Role_View === 1;
        }

        if(hasAccess) {
            // Get reportDetails, documentContent, documentHeader, and documentFooter from args
            const { reportDetails, documentContent, documentHeader, documentFooter } = args;

            // Validate inputs
            if (!documentContent) {
                console.log('Document content is empty or not provided.');
                throw ('CDG0023');
            }

            if (!reportDetails) {
                console.log('Report details is empty or not provided.');
                throw ('CDG0024');
            }

            // Parse reportDetails if it's a string (JSON)
            let parsedReportDetails = reportDetails;
            if (typeof reportDetails === 'string') {
                try {
                    parsedReportDetails = JSON.parse(reportDetails);
                } catch (parseError) {
                    console.log('Error parsing reportDetails JSON:', parseError);
                    throw ('CDG0025');
                }
            }

            // Resolve placeholders in the document content
            const resolvedContent = resolvePlaceholders(documentContent, parsedReportDetails);

            // Resolve placeholders in the document header (if provided)
            const resolvedHeader = documentHeader ? resolvePlaceholders(documentHeader, parsedReportDetails) : null;

            // Resolve placeholders in the document footer (if provided)
            const resolvedFooter = documentFooter ? resolvePlaceholders(documentFooter, parsedReportDetails) : null;

            // Destroy DB connection
            organizationDbConnection ? organizationDbConnection.destroy() : null;

            // Return the resolved content
            return {
                errorCode: '',
                message: 'Placeholder content resolved successfully.',
                resolvedContent: resolvedContent,
                resolvedHeader: resolvedHeader,
                resolvedFooter: resolvedFooter,
            };
        }else{
            console.log('Login employee id does not have view access to the form.');
            throw ('_DB0100');
        }
    }catch(error) {
        console.log('Error in the returnResolvedPlaceholderContent function main catch block. ',error);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(error, 'CDG0026');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    }
};