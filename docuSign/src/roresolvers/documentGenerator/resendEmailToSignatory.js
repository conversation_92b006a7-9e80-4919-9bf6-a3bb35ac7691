//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require moment
const moment = require('moment');
//Require app constants
const { urlEncryption,defaultValues,awsSesTemplates, formIds, formName,systemLogs } = require('../../../common/appconstants');
//Require common functions
const { sendAuthorizerEmail } = require('../../../common/documentGeneratorCommonFunctions');
//Require table alias
const { ehrTables } = commonLib.tableAlias;


//Send an email with the document
module.exports.resendEmailToSignatory = async (parent, args, context, info) => {
    console.log('Inside resendEmailToSignatory function: ');
    let organizationDbConnection;
    try{
        if(!(args.documentId > 0) || !(args.documentName) || !(args.signatoryId) || !(args.signatoryEmailAddress) || !(args.signatoryName)){
            console.log('Input document id or document name or signatory employee id is empty: ',args);
            throw ('_EC0007');
        }
        //Create DB connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Get login employee id
        let loginEmployeeId = context.Employee_Id;
        //Get the current date time based on the login employee timezone
        let loginEmployeeCurrentDateTime = moment().utc().format('YYYY-MM-DD HH:mm:ss');
        //Get the expiry time in UTC
        let urlExpiryTime = moment.utc().add(30,'days').format('YYYY-MM-DD HH:mm:ss');

        let newAuthorizerDetails = {
            Expire_Time: urlExpiryTime,
            Last_Updated_On: loginEmployeeCurrentDateTime,
            Last_Updated_By: loginEmployeeId
        };

        const [documentData, documentSignatureData] = await Promise.all([
            organizationDbConnection(ehrTables.empGeneratedDocuments).where('Generated_Document_Id', args.documentId).first(),
            organizationDbConnection(ehrTables.generatedDocumentSignatureDetails).where('Generated_Document_Id',args.documentId)
            .where('Authorizer_Id',args.signatoryId).first()
        ])

        if(documentSignatureData){
            await organizationDbConnection(ehrTables.generatedDocumentSignatureDetails).update(newAuthorizerDetails)
            .where('Generated_Document_Id',args.documentId).where('Authorizer_Id',args.signatoryId);
        }else{
            newAuthorizerDetails.Generated_Document_Id = args.documentId;
            newAuthorizerDetails.Authorizer_Id = args.signatoryId;
            newAuthorizerDetails.Is_Candidate = args.isCandidate ? 1 : 0;
            newAuthorizerDetails.Expiry_Type = 'Days';
            newAuthorizerDetails.Added_On = loginEmployeeCurrentDateTime;
            newAuthorizerDetails.Added_By = loginEmployeeId;
            
            await organizationDbConnection(ehrTables.generatedDocumentSignatureDetails).insert(newAuthorizerDetails)
        }

        let templateName=await getTemplateName(organizationDbConnection,args.documentId);
        let emailArgs = {
            orgCode: context.Org_Code,
            encryptionKey: urlEncryption.encryptionKey,
            signatureUrlPath: defaultValues.signatureUrlPath,
            firstAuthorizerEmployeeId: args.signatoryId,
            generatedDocumentId: args.documentId,
            toEmployeeEmail: args.signatoryEmailAddress,
            toEmployeeName: args.signatoryName,
            documentName: args.documentName,
            sesTemplateName: awsSesTemplates.sendDocumentLinkToSign,
            templateName:templateName?.Template_Name,
            addedBy:templateName?.Added_By,
            sendEmailToCandidate:args.isCandidate,
        };
        let systemLogParam = {
            action: systemLogs.roleUpdate,
            userIp: context.User_Ip,
            employeeId: loginEmployeeId,
            formId: formIds.docuSign,
            formName: formName.docuSign,
            message: `The ${args.documentName} document has been resent to ${args?.signatoryName || 'the signatory'} for signature`,
            organizationDbConnection: organizationDbConnection,
            uniqueId: documentData?.Candidate_Id,
            isEmployeeTimeZone: 0,
        };
        
        //Send an email to the authorizer
        let emailResponse = await sendAuthorizerEmail(organizationDbConnection,emailArgs);
        if(emailResponse === 'failure'){
            systemLogParam.message = `The ${args.documentName} document has been failed resent to ${args?.signatoryName || 'the signatory'} for signature`;
            await commonLib.func.createSystemLogActivities(systemLogParam);
            throw 'PBP0105';
        }else{
            await commonLib.func.createSystemLogActivities(systemLogParam);
            return {errorCode:'',message:'Email resend successfully.'};
        }
        
    }catch(mainCatchErr) {
        console.log('Error in the resendEmailToSignatory function main catch block: ',mainCatchErr);
        let errResult = commonLib.func.getError(mainCatchErr, 'CDG0124');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    } finally {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
};

async function getTemplateName(organizationDbConnection,documentId){
    try{
        let templateNameArray=await organizationDbConnection(ehrTables.empGeneratedDocuments)
        .select('Template_Name','Added_By').where('Generated_Document_Id',documentId).first();
        return templateNameArray || {};
    }
    catch(mainCatchErr) {
        throw mainCatchErr;
    }
}