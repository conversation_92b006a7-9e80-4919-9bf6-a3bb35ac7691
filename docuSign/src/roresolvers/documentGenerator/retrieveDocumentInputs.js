//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require common functions
const { groupTemplateFieldsByEntity } = require('../../../common/documentGeneratorCommonFunctions');
//Require table alias
const { ehrTables } = commonLib.tableAlias;
//Require local table alias
const { ehrTables: localEhrTables } = require('../../../common/tableAlias');

async function formQuery(organizationDbConnection,retrieveArgs){
    try{
        let {mainTableName,mainTableAliasName,uniqueFieldName,uniqueFieldWithAlias,entityDetails,entityDetailsLength,uniqueId} = retrieveArgs;
        let tableNameInArray = [];
        let selectFieldsArray = [];
        let selectFieldIndex = '';
        //Fields that has to be fetch using the knex raw statement
        let rawFields = ['Employee Id','Employee Name','Manager Name','Employee Permanent Address','Employee Current Address','Candidate Name'];
        let retrieveQuery =  organizationDbConnection(mainTableName+' as '+mainTableAliasName);
        //Iterate the template fields
        for(let i=0; i<entityDetailsLength;i++){
            //Get the template field name
            let fieldName = entityDetails[i].Field_Name;
            //Get the table name.Example: emp_personal_info
            let tableName = entityDetails[i].Table_Name;
            //Get the column name.Example: Employee_Id
            let mainTableFieldIndex = entityDetails[i].Field_Index;
            //Subordinate table alias name
            let subordinateTableAliasName = '';
            /** If we need to get the value for the template fields using more than one table
             * we will have the subordinate table details. For example, if we need to present the
             * designation for the employee we need to use emp_job and designation table to get it.
             * Get the subordinate table column name.Example: Designation_Name */
            let subordinateTableFieldIndex = entityDetails[i].Subordinate_Table_Field_Index;
            //Example: Employee_Id. Column name in the subordinate table name
            let subordinateTableJoinKey = entityDetails[i].Subordinate_Table_Join_Key;

            /**If we already formed the join statement using the input table,
             * then we should not form the join statement again.So validate the table name
             * already exist in the table name array.*/
            if(!tableNameInArray.includes(tableName)){
                if(tableName===ehrTables.resignation){
                    retrieveQuery = retrieveQuery.leftJoin(tableName+' as '+tableName, function() {
                        this.on(tableName+'.'+uniqueFieldName, uniqueFieldWithAlias)
                            .onIn(tableName+'.Approval_Status', ['Applied','Approved','Incomplete'])
                    })
                }else{
                    //Example response: leftJoin('emp_job as emp_job', 'emp_job.Employee_Id', 'EPI.Employee_Id')
                    retrieveQuery = retrieveQuery.leftJoin(tableName+' as '+tableName,tableName+'.'+uniqueFieldName,uniqueFieldWithAlias);
                }
                tableNameInArray.push(tableName);
            }
            /** If the subordinate field index exists */
            if(subordinateTableFieldIndex){
                let subordinateTableName = entityDetails[i].Subordinate_Table_Name;
                subordinateTableAliasName = entityDetails[i].Subordinate_Table_Alias_Name;
                //Example response: leftJoin('designation as jobDesignation', 'emp_job.Employee_Id', 'jobDesignation.Designation_Id')
                retrieveQuery = retrieveQuery.leftJoin(subordinateTableName+' as '+subordinateTableAliasName,tableName+'.'+mainTableFieldIndex,subordinateTableAliasName+'.'+subordinateTableJoinKey);
            }

            //If field has to be fetched using the knex raw statement
            if(rawFields.includes(fieldName)){
                selectFieldIndex = (subordinateTableFieldIndex) ? subordinateTableFieldIndex : mainTableFieldIndex;
                let rawSelectStatement='';
                if(fieldName=='Employee Id'){
                    rawSelectStatement = organizationDbConnection.raw("(CASE WHEN employeeEmpJob.User_Defined_EmpId IS NOT NULL THEN employeeEmpJob.User_Defined_EmpId ELSE employeeEmpJob.Employee_Id END) as Employee_Id");
                }else if(fieldName=='Employee Name'){
                    rawSelectStatement = organizationDbConnection.raw("concat(emp_personal_info.Emp_First_Name,' ',emp_personal_info.Emp_Last_Name) as Employee_Name");
                }else if(fieldName=='Manager Name'){
                    rawSelectStatement = organizationDbConnection.raw("concat(employeeEmpPersonalInfo.Emp_First_Name,' ',employeeEmpPersonalInfo.Emp_Last_Name) as Manager_Name");
                }else if(fieldName=='Employee Permanent Address'){
                    rawSelectStatement = organizationDbConnection.raw("concat_ws(',',contact_details.pApartment_Name,contact_details.pStreet_Name,contact_details.pCity,contact_details.pState,contact_details.pCountry) as Employee_Permanent_Address");
                }else if(fieldName=='Employee Current Address'){
                    rawSelectStatement = organizationDbConnection.raw("concat_ws(',',contact_details.cApartment_Name,contact_details.cStreet_Name,contact_details.cCity,contact_details.cstate,contact_details.ccountry) as Employee_Current_Address");
                }else if(fieldName=='Candidate Name'){
                    rawSelectStatement = organizationDbConnection.raw("concat_ws(' ',candidate_personal_info.Emp_First_Name,candidate_personal_info.Emp_Middle_Name,candidate_personal_info.Emp_Last_Name) as Candidate_Name");
                }
                if(rawSelectStatement){
                    selectFieldIndex = rawSelectStatement;
                }
            }else{
                selectFieldIndex = (subordinateTableFieldIndex) ? subordinateTableAliasName+'.'+subordinateTableFieldIndex : tableName+'.'+mainTableFieldIndex;
            }
            /** Push the column name(field_index) in the select fields array.*/
            selectFieldsArray.push(selectFieldIndex);
        }
        console.log('selectFieldsArray',selectFieldsArray);
        //Run the query
        let queryResult = await retrieveQuery.select(selectFieldsArray)

        .where(uniqueFieldWithAlias,uniqueId)
        .groupBy(uniqueFieldWithAlias)
        .then();

        return queryResult;
    }catch(mainCatchError){
        console.log('Error in the formQuery function main catch block.',mainCatchError);
        throw mainCatchError;
    }
}

//Process clause components to determine if clauses should be kept
async function processClauseComponents(organizationDbConnection, clauseIds, employeeId, candidateId){
    try{
        //If no clauseIds provided, return empty array
        if(!clauseIds || clauseIds.length === 0){
            return [];
        }

        //Fetch clause components from the database
        const clauseComponents = await organizationDbConnection(localEhrTables.clauseComponent + ' as CC')
            .select('CC.Clause_Id as clauseId', 'CC.Attribute_Mapping as attributeMapping')
            .whereIn('CC.Clause_Id', clauseIds);

        //Array to store clause data results
        let clauseDataArray = [];

        //Process each clause component
        for(let clause of clauseComponents){
            let keepClauseContent = false;

            if(clause.attributeMapping){
                try{
                    //Parse the JSON attribute mapping
                    const attributeMapping = JSON.parse(clause.attributeMapping);

                    let queryToExecute = null;
                    let idValue = null;

                    //Determine which query to use based on candidateId or employeeId
                    if(candidateId && candidateId > 0 && attributeMapping.candidateQuery){
                        queryToExecute = attributeMapping.candidateQuery;
                        idValue = candidateId;
                    }else if(employeeId && employeeId > 0 && attributeMapping.employeeQuery){
                        queryToExecute = attributeMapping.employeeQuery;
                        idValue = employeeId;
                    }

                    //Execute the query if available
                    if(queryToExecute && idValue){
                        //Validate that idValue is a number to prevent SQL injection
                        const sanitizedId = parseInt(idValue, 10);
                        if(isNaN(sanitizedId)){
                            console.log('Invalid ID value for clauseId', clause.clauseId);
                            keepClauseContent = false;
                        }else{
                            //Use Knex's parameter binding to prevent SQL injection
                            //Replace ? placeholders with the sanitized ID value
                            const queryParams = [];
                            let parameterizedQuery = queryToExecute;

                            //Count the number of ? placeholders and create parameter array
                            const placeholderCount = (queryToExecute.match(/\?/g) || []).length;
                            for(let i = 0; i < placeholderCount; i++){
                                queryParams.push(sanitizedId);
                            }

                            //Execute the query with proper parameter binding
                            const queryResult = await organizationDbConnection.raw(parameterizedQuery, queryParams);

                            //Check if record_count is not 0
                            if(queryResult && queryResult[0] && queryResult[0].length > 0){
                                const firstRow = queryResult[0][0];
                                if(!firstRow || typeof firstRow.record_count === 'undefined'){
                                    console.log('Query result missing record_count field for clauseId', clause.clauseId);
                                    keepClauseContent = false;
                                }else{
                                    const recordCount = firstRow.record_count || 0;
                                    keepClauseContent = recordCount !== 0;
                                }
                            }
                        }
                    }
                }catch(parseError){
                    console.log('Error parsing or executing query for clauseId', clause.clauseId, ':', parseError);
                    //If there's an error, default to false
                    keepClauseContent = false;
                }
            }

            //Add the result for this clause to the array
            clauseDataArray.push({
                clauseId: clause.clauseId,
                keepClauseContent: keepClauseContent
            });
        }

        return clauseDataArray;
    }catch(mainCatchError){
        console.log('Error in the processClauseComponents function main catch block.',mainCatchError);
        throw mainCatchError;
    }
}

//retrieve the employee document inputs
module.exports.retrieveDocumentInputs = async (parent, args, context, info) => {
    console.log('Inside retrieveDocumentInputs() function.');  
    let organizationDbConnection;
    let errResult;
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);

        if(args.documentTemplateId && args.employeeId>=0 && ([0,1].includes(args.registeredBusinessAddress)) &&
        args.candidateId >=0){
            //Retrieve the template from the document template engine table
            return(
                organizationDbConnection(ehrTables.documentTemplateEngine)
                .select('Document_Template_Id as documentTemplateId','Template_Content as templateContent',
                'Template_Fields as templateFields')
                .where('Document_Template_Id',args.documentTemplateId)
                .then(async(documentTemplateDetails)=>{
                    if(documentTemplateDetails && documentTemplateDetails.length > 0){
                        //Get the business address details
                        let businessAddressQuery = organizationDbConnection(ehrTables.location+' as L')
                        .select('L.Street1 as street1','L.Street2 as street2','L.City_Id as cityId',
                        'L.State_Id as stateId','L.Country_Code as countryCode','L.Pincode as pincode',
                        'S.State_Name as state','C.City_Name as city','CO.Country_Name as country')
                        .leftJoin(ehrTables.state+' as S','L.State_Id','S.State_Id')
                        .leftJoin(ehrTables.city+' as C','L.City_Id','C.City_Id')
                        .leftJoin(ehrTables.country+' as CO','L.Country_Code','CO.Country_Code')

                        if(args.registeredBusinessAddress === 1){
                            businessAddressQuery = businessAddressQuery.where('L.Location_Type','MainBranch');
                        }else{
                            businessAddressQuery = businessAddressQuery.innerJoin(ehrTables.empJob+' as EJ','L.Location_Id','EJ.Location_Id')
                                                .where('EJ.Employee_Id',args.employeeId);
                        }

                        let businessAddress = await businessAddressQuery
                        .then(businessAddressResult => {
                            return (businessAddressResult && businessAddressResult.length > 0) ? businessAddressResult[0] : '';
                        });

                        let documentTemplateFields = documentTemplateDetails[0].templateFields ? JSON.parse(documentTemplateDetails[0].templateFields) : [];
                        
                        if(documentTemplateFields && documentTemplateFields.length > 0){
                              documentTemplateFields = documentTemplateFields.map(f => {
                                const match = f.match(/\["(.+?)"\]/);
                                return match ? match[1] : f;
                            }).filter(Boolean);
                            //Retrieve the template static components from the document template engine table
                            return(
                            organizationDbConnection(ehrTables.documentTemplateFields+' as DTF')
                            .select('DTF.Group_Title','DTF.Field_Name','DTF.Display_Field_Name','DTF.Entity as entity',
                            'DTF.Table_Name','DTF.Field_Index','DTF.Subordinate_Table_Name','DTF.Subordinate_Table_Alias_Name',
                            'DTF.Subordinate_Table_Field_Index','DTF.Subordinate_Table_Join_Key')
                            .whereIn('DTF.Field_Name',documentTemplateFields)
                            .where('DTF.Is_Custom_Field',0)
                            .then(async(templateStaticComponentResult)=>{
                                let responseLength = templateStaticComponentResult.length;
                                let result =[];
                                let candidateEntityResult=[];
                                let organizationEntitiesResult = [];

                                if(responseLength>0){
                                    let templateEntityResult = await groupTemplateFieldsByEntity(templateStaticComponentResult);
                                    let candidateEntities = templateEntityResult['Candidate'] ? templateEntityResult['Candidate'] : [];
                                    let candidateEntitiesLength = candidateEntities.length;

                                    let employeeEntities = templateEntityResult['Employee'] ? templateEntityResult['Employee'] : [];
                                    let employeeEntitiesLength = employeeEntities.length;

                                    let organizationEntities = templateEntityResult['Organization'] ? templateEntityResult['Organization'] : [];
                                    let organizationEntitiesLength = organizationEntities.length;                                  
                                    if(employeeEntitiesLength > 0){
                                        let employeeMainTableAliasName = 'EPI';
                                        let employeeUniqueFieldName = 'Employee_Id';

                                        let retrieveEmployeeDetailsArgs = {
                                            mainTableName: 'emp_personal_info',
                                            mainTableAliasName: employeeMainTableAliasName,
                                            uniqueFieldName: employeeUniqueFieldName,
                                            uniqueFieldWithAlias: employeeMainTableAliasName+'.'+employeeUniqueFieldName,
                                            entityDetails: employeeEntities,
                                            entityDetailsLength: employeeEntitiesLength,
                                            uniqueId: args.employeeId
                                        };
                                        result = await formQuery(organizationDbConnection,retrieveEmployeeDetailsArgs);
                                    }

                                    if(candidateEntitiesLength > 0){
                                        let candidateMainTableAliasName = 'CPI';
                                        let candidateUniqueFieldName = 'Candidate_Id';

                                        let retrieveCandidateDetailsArgs = {
                                            mainTableName: 'candidate_personal_info',
                                            mainTableAliasName: candidateMainTableAliasName,
                                            uniqueFieldName: candidateUniqueFieldName,
                                            uniqueFieldWithAlias: candidateMainTableAliasName+'.'+candidateUniqueFieldName,
                                            entityDetails: candidateEntities,
                                            entityDetailsLength: candidateEntitiesLength,
                                            uniqueId: args.candidateId
                                        };

                                        candidateEntityResult = await formQuery(organizationDbConnection,retrieveCandidateDetailsArgs);
                                    }
                                    let isFieldForce = await commonLib.func.getOrgDetails(context.Org_Code, organizationDbConnection,0,1);
                                    if(organizationEntitiesLength > 0){

                                        if(isFieldForce){
                                            let table=employeeEntitiesLength > 0?ehrTables.empJob + " as EJ": ehrTables.candidateRecruitmentInfo+ " as CRI";
                                            let mainTableAlias=employeeEntitiesLength > 0?'EJ':'CRI';
                                            let idColumn=employeeEntitiesLength > 0?'Employee_Id':'Candidate_Id';
                                            let extraTable=employeeEntitiesLength > 0?null:ehrTables.jobPost+ " as JP";
                                            let extraIdColumn=employeeEntitiesLength > 0?null:'JP.Job_Post_Id';
                                          let selectquery=[];
                                          let serviceProviderAlias='SP'
                                          let id=employeeEntitiesLength > 0?args.employeeId:args.candidateId;
                                          let extraTableAlias=employeeEntitiesLength > 0?null:'JP';
                                          organizationEntities.forEach((element, i) => {
                                            if (element.Field_Name === 'Organization Name') {
                                                selectquery.push(serviceProviderAlias + '.' + 'Service_Provider_Name as Organization_Name');
                                            }
                                            if (element.Field_Name === 'Organization Logo') {
                                                selectquery.push(serviceProviderAlias + '.' + 'Service_Provider_Logo as Organization_Logo');
                                            }
                                        });
                                          organizationEntitiesResult=await getserviceProvideDetails(organizationDbConnection,id,selectquery,table,mainTableAlias,idColumn,extraTable,extraIdColumn,extraTableAlias)
                                        }
                                        else{
                                            let orgMainTableAliasName = 'CPI';
                                        let orgUniqueFieldName = 'Org_Code';

                                        let retrieveOrgDetailsArgs = {
                                            mainTableName: 'org_details',
                                            mainTableAliasName: orgMainTableAliasName,
                                            uniqueFieldName: orgUniqueFieldName,
                                            uniqueFieldWithAlias: orgMainTableAliasName+'.'+orgUniqueFieldName,
                                            entityDetails: organizationEntities,
                                            entityDetailsLength: organizationEntitiesLength,
                                            uniqueId: context.Org_Code
                                        };

                                        organizationEntitiesResult = await formQuery(organizationDbConnection,retrieveOrgDetailsArgs);
                                        }
                                        
                                    }
                                    if(organizationEntitiesResult && organizationEntitiesResult.length > 0){
                                        if(organizationEntitiesResult[0].Organization_Logo){
                                            //call function to form s3 file path for organization logo
                                            let reportLogoS3Path = '';
                                            if(isFieldForce){
                                                reportLogoS3Path = process.env.domainName + '_upload/' + context.Org_Code + '_tmp/logos/' + 'service_provider/'+organizationEntitiesResult[0].Organization_Logo;  
                                            }
                                            else{
                                                reportLogoS3Path = await commonLib.func.formS3FilePath(organizationEntitiesResult[0].Organization_Logo, context.Org_Code, 'hrapplogo', '', process.env.domainName);

                                            }
                                            reportLogoS3Path = reportLogoS3Path ? await commonLib.func.getFileURL(process.env.region, process.env.logoBucket, reportLogoS3Path) : '';
                                            reportLogoS3Path = reportLogoS3Path ? reportLogoS3Path.split('?') : '';

                                            organizationEntitiesResult[0].Organization_Logo = (reportLogoS3Path && reportLogoS3Path[0]) ? reportLogoS3Path[0] : '';
                                        }
                                    }

                                }else{
                                    console.log('Empty static component list.');
                                }
                                
                                //For the employee entity get the custom fields where the user inputs are zero
                                return(
                                organizationDbConnection(ehrTables.documentTemplateFields+' as DTF')
                                .pluck('DTF.Field_Name')
                                .whereIn('DTF.Field_Name',documentTemplateFields)
                                .where('DTF.Is_Custom_Field',1)
                                .where('DTF.Custom_Field_User_Input',0)
                                .where('DTF.Entity','Employee')
                                .then(async(templateCustomFieldResult)=>{
                                    let employeeCustomFields = {};
                                    let previousDepartment = '',
                                    previousDesignation = '',
                                    previousLocation = '',
                                    managerDesignation='';
                                    //If the custom field exist without user input
                                    if(templateCustomFieldResult.length > 0){
                                        for(let customFieldWithoutInput of templateCustomFieldResult){
                                            if(customFieldWithoutInput === 'Previous Department'){
                                                previousDepartment = await organizationDbConnection(ehrTables.empDepartmentHistory+' as EDH')
                                                .first('D.Department_Name')
                                                .innerJoin(ehrTables.department+' as D','EDH.Previous_Department_Id','D.Department_Id')
                                                .where('EDH.Employee_Id',args.employeeId)
                                                .orderBy('EDH.To_Date','desc')
                                                .then(previousDepartmentResult => {
                                                    return (previousDepartmentResult && Object.keys(previousDepartmentResult).length > 0) ? previousDepartmentResult.Department_Name : '';
                                                });
                                                employeeCustomFields.Previous_Department = previousDepartment ? previousDepartment : '';
                                            }else if(customFieldWithoutInput === 'Previous Designation'){
                                                previousDesignation = await organizationDbConnection(ehrTables.empDesignationHistory+' as EDH')
                                                .first('D.Designation_Name')
                                                .innerJoin(ehrTables.designation+' as D','EDH.Previous_Designation_Id','D.Designation_Id')
                                                .where('EDH.Employee_Id',args.employeeId)
                                                .orderBy('EDH.To_Date','desc')
                                                .then(previousDesignationResult => {
                                                    return (previousDesignationResult && Object.keys(previousDesignationResult).length > 0) ? previousDesignationResult.Designation_Name : '';
                                                });
                                                employeeCustomFields.Previous_Designation = previousDesignation ? previousDesignation : '';
                                            }else if(customFieldWithoutInput === 'Previous Location'){
                                                previousLocation = await organizationDbConnection(ehrTables.empLocationHistory+' as ELH')
                                                .first('L.Location_Name')
                                                .innerJoin(ehrTables.location+' as L','ELH.Previous_Location_Id','L.Location_Id')
                                                .where('ELH.Employee_Id',args.employeeId)
                                                .orderBy('ELH.To_Date','desc')
                                                .then(previousLocationResult => {
                                                    return (previousLocationResult && Object.keys(previousLocationResult).length > 0) ? previousLocationResult.Location_Name : '';
                                                });
                                                employeeCustomFields.Previous_Location = previousLocation ? previousLocation : '';
                                            }else{
                                                managerDesignation = await organizationDbConnection(ehrTables.empJob+' as EJ')
                                                .select('D.Designation_Name')
                                                .leftJoin(ehrTables.empJob+' as MEJ','EJ.Manager_Id','MEJ.Employee_Id')
                                                .innerJoin(ehrTables.designation+' as D','D.Designation_Id','MEJ.Designation_Id')
                                                .where('EJ.Employee_Id',args.employeeId)
                                                .then(designationResult => {
                                                    return (designationResult && designationResult.length > 0) ? designationResult[0].Designation_Name : '';
                                                });
                                                employeeCustomFields.Manager_Designation = managerDesignation ? managerDesignation : '';
                                            }
                                        }
                                        if(result && result.length > 0){
                                            result = {...result[0],...employeeCustomFields};
                                        }else{
                                            result.push(employeeCustomFields);
                                            result = result[0];
                                        }
                                    }else{
                                        result = result[0];
                                    }

                                    let candidateCustomFieldDetails = {
                                        Candidate_Designation: '',
                                        Candidate_Department:'',
                                    };

                                    //For the candidate entity get the custom fields where the user inputs are zero
                                    return(
                                    organizationDbConnection(ehrTables.documentTemplateFields+' as DTF')
                                    .pluck('DTF.Field_Name')
                                    .whereIn('DTF.Field_Name',documentTemplateFields)
                                    .where('DTF.Is_Custom_Field',1)
                                    .where('DTF.Custom_Field_User_Input',0)
                                    .where('DTF.Entity','Candidate')
                                    .then(async(candidateTemplateCustomFieldResult)=>{
                                        let candidateDesignation='';
                                        let CandidateDepartment='';                                        //If the custom field exist without user input
                                        if(candidateTemplateCustomFieldResult.length > 0){
                                            for(let candidateCustomFieldWithoutInput of candidateTemplateCustomFieldResult){
                                                console.log(candidateCustomFieldWithoutInput,"candidateCustomFieldWithoutInput")
                                                if(candidateCustomFieldWithoutInput === 'Candidate Designation'){
                                                    candidateDesignation = await organizationDbConnection(ehrTables.candidateRecruitmentInfo+' as CRJ')
                                                    .select('D.Designation_Name')
                                                    .leftJoin(ehrTables.jobPost+' as JP','CRJ.Job_Post_Id','JP.Job_Post_Id')
                                                    .innerJoin(ehrTables.designation+' as D','JP.Designation','D.Designation_Id')
                                                    .where('CRJ.Candidate_Id',args.candidateId)
                                                    .then(designationResult => {
                                                        return (designationResult && designationResult.length > 0) ? designationResult[0].Designation_Name : '';
                                                    });
                                                    candidateCustomFieldDetails.Candidate_Designation =  candidateDesignation ? candidateDesignation : '';
                                                }
                                                if(candidateCustomFieldWithoutInput === 'Candidate Department'){
                                                    CandidateDepartment = await organizationDbConnection(ehrTables.candidateRecruitmentInfo+' as CRJ')
                                                    .select('D.Department_Name')
                                                    .leftJoin(ehrTables.jobPost+' as JP','CRJ.Job_Post_Id','JP.Job_Post_Id')
                                                    .leftJoin(ehrTables.department+' as D', 'JP.Functional_Area', 'D.Department_Id')
                                                    .where('CRJ.Candidate_Id',args.candidateId)
                                                    .then(candidateDepartmentResult => {
                                                        return (candidateDepartmentResult && candidateDepartmentResult.length > 0) ? candidateDepartmentResult[0].Department_Name : '';
                                                    });
                                                    candidateCustomFieldDetails.Candidate_Department=CandidateDepartment?CandidateDepartment:'';
                                                }
                                            }
                                        }

                                        if(candidateEntityResult && candidateEntityResult.length > 0){
                                            candidateEntityResult[0].Candidate_Designation = candidateCustomFieldDetails.Candidate_Designation;
                                            candidateEntityResult[0].Candidate_Department = candidateCustomFieldDetails.Candidate_Department;
                                        }else{
                                            candidateEntityResult.push(candidateCustomFieldDetails);
                                        }

                                        //Combine all the employee, candidate and organization results
                                        let documentDetailsResult = {};

                                        if(result && Object.keys(result).length > 0){
                                            documentDetailsResult = result;//employee entity
                                        }

                                        if(candidateEntityResult && candidateEntityResult.length > 0){
                                            documentDetailsResult = {...documentDetailsResult,...candidateEntityResult[0]};//candidate entity
                                        }
                                        if(organizationEntitiesResult && organizationEntitiesResult.length > 0){
                                            documentDetailsResult = {...documentDetailsResult,...organizationEntitiesResult[0]};//organization entity
                                        }

                                        //Process clause components if clauseId is provided
                                        if(args.clauseId && args.clauseId.length > 0){
                                            const clauseDataArray = await processClauseComponents(
                                                organizationDbConnection,
                                                args.clauseId,
                                                args.employeeId,
                                                args.candidateId
                                            );
                                            //Add clauseData array to documentDetailsResult
                                            if(clauseDataArray && clauseDataArray.length > 0){
                                                documentDetailsResult.clauseData = clauseDataArray;
                                            }
                                        }

                                        //Destroy DB connection
                                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                                        return { errorCode:'',message:'Document details retrieved successfully.',
                                                documentDetails:(Object.keys(documentDetailsResult).length>0) ?JSON.stringify(documentDetailsResult) : '',
                                                businessAddress: businessAddress};
                                    })
                                    )
                                })
                                )
                            }));
                        }else{
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return success response
                            return {errorCode:'',message:'Document details retrieved successfully.',documentDetails:'',businessAddress: businessAddress};
                        }
                    }else{
                        console.log('Document template does not exist.',documentTemplateDetails);
                        throw 'CDG0121';
                    }
                })
                .catch(catchError => {
                    console.log('Error in retrieveDocumentInputs() function .catch block',catchError);
                    errResult = commonLib.func.getError(catchError, 'CDG0125');
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    //Return error response
                    throw new ApolloError(errResult.message,errResult.code);
                })
            );
        }else{
            console.log('Input does not exist.',args);
            throw '_EC0007';
        }
    }catch(retrieveDocumentInputsMainCatchErr) {
        console.log('Error in the retrieveDocumentInputs() function main catch block. ',retrieveDocumentInputsMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(retrieveDocumentInputsMainCatchErr, 'CDG0011');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    }
};

async function getserviceProvideDetails(organizationDbConnection,id,selectquery,table,mainTableAlias,idColumn,extraTable=null,extraIdColumn=null,extraTableAlias=null){
    return(
    organizationDbConnection(table).select(selectquery)
    .modify(function (queryBuilder) {
        // Conditionally add the 'trx' filter
        if (extraIdColumn) {
            queryBuilder.leftJoin(extraTable, extraIdColumn, mainTableAlias+'.Job_Post_Id')
            queryBuilder.leftJoin(ehrTables.serviceProvider + ' as SP', 'SP.Service_Provider_Id', extraTableAlias+'.Service_Provider_Id')
        }
        else{
            queryBuilder.leftJoin(ehrTables.serviceProvider + ' as SP', 'SP.Service_Provider_Id', mainTableAlias+'.Service_Provider_Id')
        }
    })
    .where(mainTableAlias+'.'+idColumn,id).then(data=>{
        return data;
    })
)
}
