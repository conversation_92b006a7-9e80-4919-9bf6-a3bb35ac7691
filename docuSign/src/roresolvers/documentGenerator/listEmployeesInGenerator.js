//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require constants
const { defaultValues, formName } = require('../../../common/appconstants');

//Retrieve the employees to be listed in document generator
module.exports.listEmployeesInGenerator = async (parent, args, context, info) => {
    console.log('Inside listEmployeesInGenerator() function.');
    let organizationDbConnection;
    let adminEmployeeIds;
    try {
        if(!commonLib.commonValidation.booleanNumberValidation(args.fetchManagerAndAdmin)
        || (!(['generatorEmployees','authorizerEmployees'].includes(args.source)))){
            throw '_EC0007';
        }else{
            //Require table alias
            const { ehrTables } = commonLib.tableAlias;

            organizationDbConnection = knex(context.connection.OrganizationDb);

            //Get the login employee - document generator form access
            let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id, formName.docuSign, '', 'UI');

            //Check view rights exist or not
            if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                console.log('Login employee id does not have view access to document generator form.');
                throw '_DB0100';
            }

            let employeeIdArray = [];

            //Get employee IDs based on access rights for generatorEmployees source
            if (args.source === 'generatorEmployees') {
                //Use getEmployeeIdsBasedOnAccessRights to determine which employees the logged-in user can access
                let { employeeIds, isAdmin } = await commonLib.func.getEmployeeIdsBasedOnAccessRights(
                    organizationDbConnection,
                    context.Employee_Id,
                    context.Org_Code,
                    checkRights
                );
                // If admin, allow empty array to show all employees
                // Otherwise, ensure array always has at least the login employee ID
                if (!isAdmin && (!employeeIds || employeeIds.length === 0)) {
                    employeeIdArray = [context.Employee_Id];
                } else {
                    employeeIdArray = employeeIds;
                }
            }

            /** If the source is generator employees get the active/inactive employee details.
             * Or if it is authorizer get the active employees. */
            let subQuery =
            organizationDbConnection(ehrTables.empPersonalInfo)
            .select('EPI.Employee_Id as employeeId',
            organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as employeeName"),
            organizationDbConnection.raw('(CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EJ.Employee_Id END) as userDefinedEmployeeId'),
            'DES.Designation_Name as designationName',
            'DES.Designation_Code as designationCode'
            )
            .from(ehrTables.empPersonalInfo+' as EPI')
            .leftJoin(ehrTables.empJob+' as EJ','EPI.Employee_Id', 'EJ.Employee_Id')
            .leftJoin(ehrTables.designation+' as DES','EJ.Designation_Id', 'DES.Designation_Id')
                    .where('EPI.Form_Status', 1)
                    .modify((queryBuilder) => {
                        //Apply employee ID filter for generatorEmployees source
                        if (args.source === 'generatorEmployees' && employeeIdArray && employeeIdArray.length > 0) {
                            queryBuilder.whereIn('EPI.Employee_Id', employeeIdArray);
                        }
                    });

            if(args.source === 'authorizerEmployees'){
                //If fetchManagerAndAdmin is 0 then all employees will be returned. If the value is 1 then only manager and admin will be returned
                if(args.fetchManagerAndAdmin === 1){

                    //Get the admin, employee admin, payroll admin, and roster admin employee ids at roles level
                    let roleLevelAdmin = await organizationDbConnection.pluck('EJ.Employee_Id')
                    .from(ehrTables.rolesBasedAccessControl + ' as RBAC')
                    .distinct()
                    .innerJoin(ehrTables.empJob+' as EJ','RBAC.Roles_Id','EJ.Roles_Id')
                    .whereIn('RBAC.Form_Id',defaultValues.adminFormIdArray)
                    .where('RBAC.Role_Update',1)
                    .then();

                    //Get the admin, employee admin, payroll admin, and roster admin employee ids at employee level roles
                    let employeeLevelAdmin = await organizationDbConnection.pluck('Employee_Id')
                    .from(ehrTables.empAccessRights)
                    .distinct()
                    .whereIn('Form_Id',defaultValues.adminFormIdArray)
                    .where('Role_Update',1)
                    .then();
    
                    //Get the admin, employee admin, payroll admin, and roster admin employee ids at designation level roles
                    let designationLevelAdmin = await organizationDbConnection.pluck('EJ.Employee_Id')
                    .from(ehrTables.ehrRoles+' as ER')
                    .distinct()
                    .innerJoin(ehrTables.empJob+' as EJ','ER.Designation_Id','EJ.Designation_Id')
                    .whereIn('ER.Form_Id',defaultValues.adminFormIdArray)
                    .where('ER.Role_Update',1)
                    .then();

                    //Concat all the admin employee ids and remove duplicates
                    let adminEmployeeIds = roleLevelAdmin.concat(employeeLevelAdmin).concat(designationLevelAdmin);
                    adminEmployeeIds = [...new Set(adminEmployeeIds)];

                    //Form the condition to get manager and admin employee ids
                    subQuery = subQuery.where((qb)=>{
                        qb.where('EPI.Is_Manager', 1)
                        qb.orWhere((qb1)=>{
                            qb1.whereIn('EPI.Employee_Id',adminEmployeeIds);
                        })
                    })
                }

                subQuery = subQuery.where('EJ.Emp_Status','Active');
            }

            return (
            subQuery
            .then((employeeResponse) => { 
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return  {errorCode: '',message: '',employeeDetails: (employeeResponse.length > 0) ? employeeResponse : []};
            })
            .catch( catchError => {
                console.log('Error in the listEmployeesInGenerator() function .catch block',catchError);
                errResult = commonLib.func.getError(catchError, 'CDG0110');
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                //Return error response
                throw new ApolloError(errResult.message,errResult.code);
            })
            )
        }
    }
    catch(mainCatchError) {
        console.log('Error in the listEmployeesInGenerator() function main catch block. ',mainCatchError);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(mainCatchError, 'CDG0011');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    } 
};