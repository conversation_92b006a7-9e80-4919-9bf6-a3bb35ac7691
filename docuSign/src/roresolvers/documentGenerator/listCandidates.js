//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require constants
const { formName,formIds } = require('../../../common/appconstants');
const {getJobPostBasedOnRole}=require('../../../common/commonFunctions')

//List the candidates details
module.exports.listCandidates = async (parent, args, context, info) => {
    console.log('Inside listCandidates() function.');
    let organizationDbConnection;
    let errResult;
    try{
        //Require table alias
        const { ehrTables } = commonLib.tableAlias;

        organizationDbConnection = knex(context.connection.OrganizationDb);
        let logInEmpId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId, formName.docuSign, '', 'UI',null,formIds.docuSign);

        // Get recruitment settings with Enforce_Candidate_Salary flag
        const recruitmentSettings = await organizationDbConnection('recruitment_settings')
            .select('Enforce_Candidate_Salary')
            .first();

        const jobPostIds = await getJobPostBasedOnRole(organizationDbConnection, checkRights, logInEmpId, context.Org_Code);
      
        return (    
            organizationDbConnection(ehrTables.candidateRecruitmentInfo + ' as R')
            .select('R.Candidate_Id as candidateId', 'R.Blacklisted as blacklisted',
                'ST.Status as status',
            organizationDbConnection.raw(`CONCAT_WS(' ', CP.Emp_First_Name, CASE WHEN CP.Emp_Middle_Name IS NOT NULL AND CP.Emp_Middle_Name <> '' THEN TRIM(CP.Emp_Middle_Name) ELSE NULL END, CP.Emp_Last_Name) as candidateName`),
            organizationDbConnection.raw(`CASE WHEN CSD.Candidate_Id IS NOT NULL THEN 'Yes' ELSE 'No' END as Salary_Exist`))
            .leftJoin(ehrTables.atsStatusTable + ' as ST', 'R.Candidate_Status', 'ST.Id')
            .leftJoin(ehrTables.candidatePersonalInfo + ' as CP', 'R.Candidate_Id', 'CP.Candidate_Id')
            .leftJoin('candidate_salary_details as CSD', 'R.Candidate_Id', 'CSD.Candidate_Id')
            .whereIn('ST.Stage_Id', [4,6,7])
            .whereNotIn('ST.Status', ['Onboarding Withdrawn','Onboarding Rejected','Onboarded'])
            .where('R.Archived', 'No')
            .groupBy('R.Candidate_Id')
            .modify((queryBuilder)=>{             
                queryBuilder.whereIn('R.Job_Post_Id', jobPostIds); 
             })
            .then(async(candidateDetails) => {
                
                //Destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                //Return response
                return {errorCode: '',message:'Candidates details retrieved successfully',candidateDetails: (candidateDetails.length > 0) ? candidateDetails : [] };
            })
            .catch(catchError => {
                console.log('Error in the listCandidates function in the .catch block.',catchError);
                //Destroy database connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                errResult = commonLib.func.getError(catchError, 'CDG0118');
                //Return error response
                throw new ApolloError(errResult.message,errResult.code);
            })
        );
    }catch(listCandidatesMainCatchErr) {
        console.log('Error in the listCandidates() function main catch block. ',listCandidatesMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(listCandidatesMainCatchErr, 'CDG0015');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    }
};