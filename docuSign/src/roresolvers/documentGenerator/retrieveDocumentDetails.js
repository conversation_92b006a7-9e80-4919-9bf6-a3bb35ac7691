//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require constants
const { defaultValues } = require('../../../common/appconstants');

//Retrieve document details for onboarding
module.exports.retrieveDocumentDetails = async (parent, args, context, info) => {
    console.log('Inside retrieveDocumentDetails function.');
    let organizationDbConnection;
    let errResult;
    try{
        //Require table alias
        const { ehrTables } = commonLib.tableAlias;
        const formId = args.formId;

        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Get the login employee - document generator form access
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            context.Employee_Id,
            '',
            '',
            'UI',
            false,
            formId
        );

        //Check view rights exist or not
        if(Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
            console.log('Login employee id does not have view access to document generator form.');
            throw '_DB0100';
        }

        //Retrieve the document details for templates with Generate_Doc_For_Onboarding = 1
        return(
            organizationDbConnection(ehrTables.documentTemplateEngine + ' as DTE')
            .select('DTE.Document_Template_Id as documentTemplateId')
            .where('DTE.Generate_Doc_For_Onboarding', 1)
            .whereNotNull('DTE.Generate_Doc_For_Onboarding')
            .then(async(templateDetails) => {
                if(templateDetails && templateDetails.length > 0) {
                    //Extract template IDs
                    const templateIds = templateDetails.map(template => template.documentTemplateId);

                    //Query emp_generated_documents with the template IDs
                    return(
                        organizationDbConnection(ehrTables.empGeneratedDocuments + ' as EGD')
                        .select('EGD.Generated_Document_Id as generatedDocumentId','EGD.Added_On as addedOn','EGD.Last_Updated_On as updatedOn','EGD.Template_Id as templateId','EGD.Document_Link as documentLink','EGD.Template_Name as templateName',
                            'EGD.Document_Name as documentName','EGD.Employee_Id as employeeId','CD.Mobile_No as mobileNumber','CCD.Mobile_No as candidateMobileNumber','CPI.Personal_Email as candidatePersonalEmail','CJ.Emp_Email as candidateJobEmail',
                            'EGD.Candidate_Id as candidateId','EGD.Status as status', 'GDC.Component_Inputs as authorizedSignatories','EJ.Emp_Email as employeeEmail','EGD.Document_Content as documentContent',
                            organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as employeeName"),
                            organizationDbConnection.raw("CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EJ.Employee_Id END as userDefinedEmployeeId"),
                            organizationDbConnection.raw("CASE WHEN EGD.Registered_Business_Address = 'Main Branch' THEN 1 ELSE 0 END as registeredBusinessAddress"),
                            organizationDbConnection.raw("CONCAT_WS(' ',CPI.Emp_First_Name,CPI.Emp_Middle_Name, CPI.Emp_Last_Name) as candidateName"),
                            organizationDbConnection.raw("CASE WHEN CJ.User_Defined_EmpId IS NOT NULL THEN CJ.User_Defined_EmpId ELSE CJ.Candidate_Id END as userDefinedCandidateId"),
                            organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as addedBy"),
                            organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as updatedBy"),
                        )
                        .leftJoin(ehrTables.empPersonalInfo+' as EPI','EGD.Employee_Id','EPI.Employee_Id')
                        .leftJoin('emp_personal_info as EPI1', 'EPI1.Employee_Id','EGD.Added_By')
                        .leftJoin('emp_personal_info as EPI2', 'EPI2.Employee_Id','EGD.Last_Updated_By')
                        .leftJoin(ehrTables.empJob+' as EJ','EGD.Employee_Id','EJ.Employee_Id')
                        .leftJoin(ehrTables.contactDetails+' as CD','EGD.Employee_Id','CD.Employee_Id')
                        .leftJoin(ehrTables.candidatePersonalInfo+' as CPI','EGD.Candidate_Id','CPI.Candidate_Id')
                        .leftJoin(ehrTables.candidateJob+' as CJ','EGD.Candidate_Id','CJ.Candidate_Id')
                        .leftJoin(ehrTables.candidateContactDetails+' as CCD','EGD.Candidate_Id','CCD.Candidate_Id')
                        .leftJoin(ehrTables.generatedDocumentCustomComponents + ' as GDC',function() {
                            this.on('EGD.Generated_Document_Id', '=', 'GDC.Generated_Document_Id')
                                .on('GDC.Component_Name', '=', organizationDbConnection.raw('?', [defaultValues.authorizedSignatoryComponentName]))
                        })
                        .whereIn('EGD.Template_Id', templateIds)
                        .then((documentDetails) => {
                            //Return success response
                            return {
                                errorCode: '',
                                message: 'Document details retrieved successfully.',
                                documentDetails: JSON.stringify(documentDetails)
                            };
                        })
                        .catch(catchError => {
                            console.log('Error in retrieveDocumentDetails function catch block', catchError);
                            errResult = commonLib.func.getError(catchError, 'CDG0175');
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                    );
                } else {
                    //Return success response with empty array
                    return {
                        errorCode: '',
                        message: 'No templates found with Generate_Doc_For_Onboarding enabled.',
                        documentDetails: '[]'
                    };
                }
            })
            .catch(catchError => {
                console.log('Error in retrieveDocumentDetails function template query catch block', catchError);
                errResult = commonLib.func.getError(catchError, 'CDG0022');
                //Return error response
                throw new ApolloError(errResult.message, errResult.code);
            })
            .finally(() => {
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
            })
        );

    } catch(mainCatchError) {
        console.log('Error in retrieveDocumentDetails() main catch block.', mainCatchError);
        // Ensure DB connection is cleaned up for pre-query failures
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }

        //Get the error message
        errResult = commonLib.func.getError(mainCatchError, 'CDG0022');
        //Throw error
        throw new ApolloError(errResult.message, errResult.code);
    }
};

