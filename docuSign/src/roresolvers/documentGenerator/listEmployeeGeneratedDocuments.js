//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require constants
const { formName,defaultValues } = require('../../../common/appconstants');

//List the employee generated documents
module.exports.listEmployeeGeneratedDocuments = async (parent, args, context, info) => {
    console.log('Inside listEmployeeGeneratedDocuments function.');
    let organizationDbConnection;
    let errResult;
    try{
        //Require table alias
        const { ehrTables } = commonLib.tableAlias;

        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Get the login employee - document generator form access
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id , formName.docuSign, '', 'UI');
        //Check view rights exist or not                 
        if(Object.keys(checkRights).length>0 && checkRights.Role_View === 1) {
            let employeeIdArray=[]
            let getdocusignAccessControl;
            if(checkRights.Employee_Role.toLowerCase() !== 'admin'){
                getdocusignAccessControl=  await organizationDbConnection('docusign_access_control').select('*');
                }
            if(getdocusignAccessControl && getdocusignAccessControl.length){
                if (getdocusignAccessControl[0].Allow_Access==='All') {
                    employeeIdArray=[]
                }
                else if(getdocusignAccessControl[0].Allow_Access==='Own'){
                    employeeIdArray=[context.Employee_Id]
                }
                else if(getdocusignAccessControl[0].Allow_Access==='Own and Reportees'){
                    if(checkRights.Is_Manager === 1){
                        let { employeeIdsArray } = await commonLib.func.getEmployeeIdsOrManagerHierarchyBasedOnRole(organizationDbConnection,1,context.Employee_Id,0,0);
                        employeeIdArray=[...employeeIdsArray]
                    }
                    else{
                        employeeIdArray=[context.Employee_Id]
                    }
    
                }
            }
            if (checkRights.Employee_Role?.toLowerCase() === 'admin' && checkRights.Admin_Scope?.toLowerCase() !== 'no restriction') {
                employeeIdArray = await commonLib.func.handleRestrictedAccess(organizationDbConnection, context.Employee_Id, checkRights);
            }
            return(
                organizationDbConnection(ehrTables.empGeneratedDocuments+' as EGD')
                .select('EGD.Generated_Document_Id as generatedDocumentId','EGD.Added_On as addedOn','EGD.Last_Updated_On as updatedOn','EGD.Template_Id as templateId','EGD.Document_Link as documentLink','EGD.Template_Name as templateName',
                    'EGD.Document_Name as documentName','EGD.Employee_Id as employeeId','CD.Mobile_No as mobileNumber','CCD.Mobile_No as candidateMobileNumber','CPI.Personal_Email as candidatePersonalEmail','CJ.Emp_Email as candidateJobEmail',
                    'EGD.Candidate_Id as candidateId','EGD.Status as status', 'GDC.Component_Inputs as authorizedSignatories','EJ.Emp_Email as employeeEmail','EGD.Document_Content as documentContent',
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as employeeName"),
                    organizationDbConnection.raw("CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EJ.Employee_Id END as userDefinedEmployeeId"),
                    organizationDbConnection.raw("CASE WHEN EGD.Registered_Business_Address = 'Main Branch' THEN 1 ELSE 0 END as registeredBusinessAddress"),
                    organizationDbConnection.raw("CONCAT_WS(' ',CPI.Emp_First_Name,CPI.Emp_Middle_Name, CPI.Emp_Last_Name) as candidateName"),
                    organizationDbConnection.raw("CASE WHEN CJ.User_Defined_EmpId IS NOT NULL THEN CJ.User_Defined_EmpId ELSE CJ.Candidate_Id END as userDefinedCandidateId"),
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name,EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as addedBy"),
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name,EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as updatedBy"),
                )
                .leftJoin(ehrTables.empPersonalInfo+' as EPI','EGD.Employee_Id','EPI.Employee_Id')
                .leftJoin('emp_personal_info as EPI1', 'EPI1.Employee_Id','EGD.Added_By')
                .leftJoin('emp_personal_info as EPI2', 'EPI2.Employee_Id','EGD.Last_Updated_By')
                .leftJoin(ehrTables.empJob+' as EJ','EGD.Employee_Id','EJ.Employee_Id')
                .leftJoin(ehrTables.contactDetails+' as CD','EGD.Employee_Id','CD.Employee_Id')
                .leftJoin(ehrTables.candidatePersonalInfo+' as CPI','EGD.Candidate_Id','CPI.Candidate_Id')
                .leftJoin(ehrTables.candidateJob+' as CJ','EGD.Candidate_Id','CJ.Candidate_Id')
                .leftJoin(ehrTables.candidateContactDetails+' as CCD','EGD.Candidate_Id','CCD.Candidate_Id')
                .leftJoin(ehrTables.generatedDocumentCustomComponents + ' as GDC',function() {
                    this.on('EGD.Generated_Document_Id', '=', 'GDC.Generated_Document_Id')
                        .on('GDC.Component_Name', '=', organizationDbConnection.raw('?', [defaultValues.authorizedSignatoryComponentName]))
                })
                .modify((queryBuilder)=>{             
                    if(employeeIdArray && employeeIdArray.length){
                        queryBuilder.whereIn('EGD.Added_By',employeeIdArray)
                    }         
                })
                .then(async(documentDetails)=>{
                    if(documentDetails && documentDetails.length > 0){
                        // Collect all unique employee IDs and candidate IDs for batch queries
                        let employeeIds = [];
                        let candidateIds = [];
                        let documentIds = [];

                        // First pass: collect all IDs and parse authorizer details
                        for(let i in documentDetails){
                            if(documentDetails[i].authorizedSignatories){
                                let authorizerDetails = documentDetails[i].authorizedSignatories;
                                authorizerDetails = authorizerDetails ? JSON.parse(authorizerDetails) : [];
                                documentDetails[i].parsedAuthorizerDetails = authorizerDetails;
                                if(authorizerDetails.length > 0){
                                    documentIds.push(documentDetails[i].generatedDocumentId);
                                    employeeIds.push( ...authorizerDetails.filter(item => ['Manager or Admin','Employee'].includes(item.signatureKey)).map((item)=> item.signatureEmployeeId));
                                    candidateIds.push( ...authorizerDetails.filter(item => ['Candidate'].includes(item.signatureKey)).map((item)=> item.signatureEmployeeId));
                                }
                            }
                        }

                        // Batch query for employee emails
                        let employeeEmails = {};
                        if(employeeIds.length > 0){
                            const employeeEmailResults = await organizationDbConnection(ehrTables.empJob)
                                .select('Employee_Id', 'Emp_Email as emailAddress')
                                .whereIn('Employee_Id', employeeIds);

                            employeeEmailResults.forEach(result => {
                                employeeEmails[result.Employee_Id] = result.emailAddress || '';
                            });
                        }

                        // Batch query for candidate emails
                        let candidateEmails = {};
                        if(candidateIds.length > 0){
                            const candidateEmailResults = await organizationDbConnection('candidate_personal_info')
                                .select('Candidate_Id', 'Personal_Email as emailAddress')
                                .whereIn('Candidate_Id', candidateIds);

                            candidateEmailResults.forEach(result => {
                                candidateEmails[result.Candidate_Id] = result.emailAddress || '';
                            });
                        }

                        // Batch query for email sent status
                        let emailSentStatus = {};
                        if(documentIds.length > 0){
                            const emailSentResults = await organizationDbConnection('generated_document_signature_details')
                                .select('Generated_Document_Id', 'Authorizer_Id')
                                .whereIn('Generated_Document_Id', documentIds);

                            emailSentResults.forEach(result => {
                                const key = `${result.Generated_Document_Id}_${result.Authorizer_Id}`;
                                emailSentStatus[key] = true;
                            });
                        }

                        // Second pass: populate email data using batch query results
                        for(let i in documentDetails){
                            if(documentDetails[i].parsedAuthorizerDetails){
                                let authorizerDetails = documentDetails[i].parsedAuthorizerDetails;

                                for(let j in authorizerDetails){
                                    let authorizer = authorizerDetails[j];

                                    // Set email ID from batch results
                                    if(['Manager or Admin','Employee'].includes(authorizer.signatureKey)){
                                        authorizerDetails[j].emailId = employeeEmails[authorizer.signatureEmployeeId] || '';
                                    }else if(authorizer.signatureKey === 'Candidate'){
                                        authorizerDetails[j].emailId = candidateEmails[authorizer.signatureEmployeeId] || '';
                                    }

                                    // Set email sent status from batch results
                                    const emailSentKey = `${documentDetails[i].generatedDocumentId}_${authorizer.signatureEmployeeId}`;
                                    authorizerDetails[j].emailSent = emailSentStatus[emailSentKey] || false;
                                }

                                documentDetails[i].authorizedSignatories = JSON.stringify(authorizerDetails);
                                // Clean up temporary property
                                delete documentDetails[i].parsedAuthorizerDetails;
                            }
                        }
                    }
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    //Return success response
                    return { errorCode:'',message:'Employee generated documents retrieved successfully.',documentDetails:(documentDetails.length>0)?documentDetails:[]};
                })
                .catch(catchError => {
                    console.log('Error in listEmployeeGeneratedDocuments function catch block',catchError);
                    errResult = commonLib.func.getError(catchError, 'CDG0104');
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    //Return error response
                    throw new ApolloError(errResult.message,errResult.code);
                })
            );
        }else{
            console.log('Login employee id does not have view access to document generator form.');
            throw ('_DB0100');
        }
    }catch(listEmployeeGeneratedDocumentsMainCatchErr) {
        console.log('Error in the listEmployeeGeneratedDocuments function main catch block. ',listEmployeeGeneratedDocumentsMainCatchErr);
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(listEmployeeGeneratedDocumentsMainCatchErr, 'CDG0005');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    }
};