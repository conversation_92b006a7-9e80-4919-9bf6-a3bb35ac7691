//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');

//Send an email with the document
module.exports.sendDocumentInEmail = async (parent, args, context, info) => {
    console.log('Inside sendDocumentInEmail() function.');
    let errResult;
    let validationError={};
    try{
        let totalEmailAddressCount = 0;

        if(!(args.documentId > 0) || !(args.s3DocumentName)){
            console.log('Input document id or s3document name is empty',args);
            throw ('_EC0007');
        }

        //validate to email address
        if(!(args.toEmailIds)){
            validationError['IVE0226'] = commonLib.func.getError('', 'IVE0226').message;
        }else{
            totalEmailAddressCount += args.toEmailIds.length;
            if(args.ccEmailIds){
                totalEmailAddressCount += args.ccEmailIds.length;

                if(totalEmailAddressCount > 50){
                    validationError['IVE0227'] = commonLib.func.getError('', 'IVE0227').message;                    
                }
            }
        }

        // validate emailBody
        if (!args.emailBody || commonLib.commonValidation.checkLength(args.emailBody,5,500) === false) {
            validationError['IVE0117'] = commonLib.func.getError('', 'IVE0117').message2;
        }
        // validate emailSubject
        if (!args.emailSubject || commonLib.commonValidation.checkLength(args.emailSubject,5,150) === false) {
            validationError['IVE0116'] = commonLib.func.getError('', 'IVE0116').message2;
        }
    
        if(Object.keys(validationError).length === 0){
            let mimeMessage = require('mimemessage');
            let mailContent = mimeMessage.factory({contentType: 'multipart/mixed',body: []});

            mailContent.header('From', process.env.emailFrom);
            mailContent.header('To',args.toEmailIds);
            mailContent.header('Cc',args.ccEmailIds);
            mailContent.header('Subject', args.emailSubject);//Document should be fetched as an input

            let alternateEntity = mimeMessage.factory({
                contentType: 'multipart/alternate',
                body: []
            });

            let plainEntity = mimeMessage.factory({
                body: args.emailBody
            });

            alternateEntity.body.push(plainEntity);

            mailContent.body.push(alternateEntity);

            let s3DocumentName = args.s3DocumentName;
            let retrievalFileName = process.env.domainName+"/"+context.Org_Code+"/"+"Document Generator/"+s3DocumentName;

            //Split the file name: 6/6?2022-03-21 11:50:37?Appointment Letter.pdf
            let splitS3Name = s3DocumentName.split('/');//['6','6?2022-03-21 11:50:37?Appointment Letter.pdf']
            let s3FileName = splitS3Name[1].split('?');//['6','2022-03-21 11:50:37','Appointment Letter.pdf']
            let pdfFileName = s3FileName[s3FileName.length-1];//Appointment Letter.pdf
            pdfFileName = pdfFileName.replace(/\s+/g, '-');
            
            // require aws-sdk to use aws services
            const AWS = require('aws-sdk');

            // Create object for s3 bucket
            const s3 = new AWS.S3({ region: process.env.region });

            let s3Params = {
                Bucket: process.env.encryptedDocBucket, 
                Key: retrievalFileName
            };
            let s3DocumentDetails = '';

            try{
                s3DocumentDetails = await s3.getObject(s3Params).promise();
            }catch(s3CatchError){
                console.log('Error while retrieving the s3 document details in the sendDocumentInEmail() function catch block.',s3CatchError);
                throw 'PBP0110';
            }

            if(s3DocumentDetails)
            {
                let attachmentEntity = mimeMessage.factory({
                contentType: 'text/plain',
                contentTransferEncoding: 'base64',
                body: s3DocumentDetails.Body.toString('base64').replace(/([^\0]{76})/g, "$1\n")
                });
                attachmentEntity.header('Content-Disposition', 'attachment ;filename='+pdfFileName);

                mailContent.body.push(attachmentEntity);

                let notificationParams = {RawMessage: { Data: mailContent.toString() }};

                try{
                    const emailSes = new AWS.SES({ region: process.env.sesTemplatesRegion });
                    console.log('process.env.sesTemplatesRegion',process.env.sesTemplatesRegion)
                    await emailSes.sendRawEmail(notificationParams).promise();
                    return {errorCode:'',message:'Email sent successfully.'};
                }catch(emailCatchError){
                    console.log('Error while sending the email in the sendDocumentInEmail() function catch block.',emailCatchError);
                    throw 'PBP0105';
                }
            }else{
                throw 'CDG0116';
            }
        }else{
            console.log('Validation error in sendDocumentInEmail() function.', validationError);
            // throw validation error
            throw ('IVE0000');
        }
    }catch(mainCatchErr) {
        console.log('Error in the sendDocumentInEmail() function main catch block. ',mainCatchErr);
        // check error based on that return response
        if (mainCatchErr === 'IVE0000') {
            errResult = commonLib.func.getError(mainCatchErr, 'IVE0000');
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }else{
            errResult = commonLib.func.getError(mainCatchErr, 'CDG0014');
            //Return error response
            throw new ApolloError(errResult.message,errResult.code);
        }
    }
};