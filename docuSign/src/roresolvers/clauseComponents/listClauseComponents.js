//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tableAlias');

//List the clause components
module.exports.listClauseComponents = async (parent, args, context, info) => {
    console.log('Inside listClauseComponents function.');
    let organizationDbConnection;
    let errResult;
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);
        //Get the login employee
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            context.Employee_Id,
            '',
            '',
            'UI',
            false,
            args.formId
        );

        //Check view rights exist or not
        if(Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
            console.log('Login employee id does not have view access to clause component form.');
            throw '_DB0100';
        }

        //Retrieve the clause components from the clause_component table
        const clauseComponentsDetails = await organizationDbConnection(ehrTables.clauseComponent + ' as CC')
            .select(
                'CC.Clause_Id as clauseId',
                'CC.Clause_Name as clauseName',
                'CC.Clause_Content as clauseContent',
                'CC.Clause_Holder as clauseHolder'
            );

        //Return success response
        const stringifyClauseComponentsDetails = JSON.stringify(clauseComponentsDetails);
        return {
            errorCode: '',
            message: 'Clause components retrieved successfully.',
            clauseComponentsDetails: stringifyClauseComponentsDetails,
        };

    } catch(mainCatchError) {
        console.log('Error in listClauseComponents() main catch block.', mainCatchError);
        //Get the error message
        errResult = commonLib.func.getError(mainCatchError, 'CDG0021');
        //Throw error
        throw new ApolloError(errResult.message, errResult.errorCode);
    } finally {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
};

