//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tableAlias');

// Helper function to build base query with all joins
const buildBaseQuery = (organizationDbConnection, formId) => {
    return organizationDbConnection(ehrTables.customComponent + ' as CT')
        .select(
            'CT.Custom_Component_Id as customComponentId',
            'CT.Custom_Component_Name as customComponentName',
            'CT.Service_Provider_Id as serviceProviderId',
            'SP.Service_Provider_Name as serviceProviderName',
            'CT.Type as type',
            'CT.Default as default',
            'CT.Content as content',
            'CT.Visibility as visibility',
            'CT.Custom_Group_Id as customGroupId',
            'CEG.Group_Name as customGroupName',
            'CT.Status as status',
            'CT.Clause_Id as clauseId',
            'CT.Added_On as addedOn',
            'CT.Updated_On as updatedOn',
            organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as updatedBy"),
            organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as addedBy")
        )
        .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "CT.Updated_By")
        .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "CT.Added_By")
        .leftJoin(ehrTables.cusEmpGroup + " as CEG", "CEG.Group_Id", "CT.Custom_Group_Id")
        .leftJoin(ehrTables.serviceProvider + " as SP", "SP.Service_Provider_Id", "CT.Service_Provider_Id")
        .modify(function (queryBuilder) {
                if(formId === 134){
                    queryBuilder.where('CT.Status', 'Active');
                }
            })
};

// Helper function to apply filters for formId 134
const applyFilters = (query, employeeId, isFieldForceEnabled) => {
    // Apply custom group filter (always applied for formId 134)
    // Show components where Custom_Group_Id is NULL OR matches employee's groups
    query = query.where(function() {
        this.whereNull('CT.Custom_Group_Id')
            .orWhereIn('CT.Custom_Group_Id', function() {
                this.select('CEGE.Group_Id')
                    .from(ehrTables.customEmployeeGroupEmployees + ' as CEGE')
                    .where('CEGE.Employee_Id', employeeId)
                    .whereIn('CEGE.Type', ['AdditionalInclusion', 'Default']);
            });
    });

    // Apply service provider filter based on field force status
    if(!isFieldForceEnabled) {
        // If field force is not enabled, get only components with null serviceProviderId
        query = query.whereNull('CT.Service_Provider_Id');
    } else {
        // If field force is enabled, filter by employee's service provider or null
        query = query.where(function() {
            this.whereNull('CT.Service_Provider_Id')
                .orWhereIn('CT.Service_Provider_Id', function() {
                    this.select('EJ.Service_Provider_Id')
                        .from(ehrTables.empJob + ' as EJ')
                        .where('EJ.Employee_Id', employeeId)
                        .whereNotNull('EJ.Service_Provider_Id');
                });
        });
    }

    return query;
};

//List the custom components
module.exports.listCustomComponents = async (parent, args, context, info) => {
    console.log('Inside listCustomComponents function.');
    const formId = args.formId;
    let organizationDbConnection;
    let errResult;
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);

        //Get the login employee - document generator form access
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            context.Employee_Id,
            '',
            '',
            'UI',
            false,
            formId
        );

        //Check view rights exist or not
        if(Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
            console.log('Login employee id does not have view access to custom component form.');
            throw '_DB0100';
        }

        // Build base query
        let query = buildBaseQuery(organizationDbConnection, formId);

        // Apply special logic only if formId is 134
        if(formId === 134) {
            // Get Field_Force from org_details
            const orgDetails = await organizationDbConnection('org_details')
                .select('Field_Force')
                .where('Org_Code', context.Org_Code)
                .first();

            const isFieldForceEnabled = !!(orgDetails && orgDetails.Field_Force);

            // Apply filters based on field force status
            query = applyFilters(query, context.Employee_Id, isFieldForceEnabled);
        }

        // Execute query
        const customComponentsDetails = await query;

        //Return success response
        return {
            errorCode: '',
            message: 'Custom components details retrieved successfully.',
            customComponentsDetails: JSON.stringify(customComponentsDetails)
        };

    }catch(listCustomComponentsMainCatchErr) {
        console.log('Error in the listCustomComponents function main catch block. ',listCustomComponentsMainCatchErr);
        errResult = commonLib.func.getError(listCustomComponentsMainCatchErr, 'CDG0020');
        //Return error response
        throw new ApolloError(errResult.message,errResult.code);
    }finally{
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
};