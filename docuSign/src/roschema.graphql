# defining custom data type
scalar Date

type Query {
  listDocumentTemplate: listDocumentTemplateResponse!
  listEmployeeGeneratedDocuments: listEmployeeGeneratedDocumentsResponse!
  listTemplatesInDropdown(templateFormId: [Int]): listTemplatesInDropdownResponse!
  listTemplateCustomComponents(
    documentTemplateId: Int!
  ): listTemplateCustomComponentsResponse!
  listDocumentTemplateFields: listDocumentTemplateFieldsResponse
  retrieveDocumentInputs(
    documentTemplateId: Int!
    registeredBusinessAddress: Int!
    employeeId: Int!
    candidateId: Int!
    clauseId: [Int]
  ): retrieveDocumentInputsResponse!
  listEmployeesInGenerator(
    source: String!
    fetchManagerAndAdmin: Int!
  ): listEmployeesInGeneratorResponse!
  sendDocumentInEmail(
    documentId: Int!
    s3DocumentName: String!
    emailSubject: String!
    emailBody: String!
    toEmailIds: [String]!
    ccEmailIds: [String]
  ): commonResponse!
  listCandidates: listCandidatesResponse!
  resendEmailToSignatory(
    documentId: Int!
    documentName: String!
    signatoryId: Int!
    signatoryEmailAddress: String!
    signatoryName: String!
    isCandidate:Int
  ): commonResponse!
  retrieveAccreditationDetails: retrieveAccreditationDetailsResponse!
  listCustomComponents(formId: Int!): listCustomComponentsResponse!
  listClauseComponents(formId: Int!): listClauseComponentsResponse!
  retrieveDocumentDetails(formId: Int!): retrieveDocumentDetailsResponse!
  returnResolvedPlaceholderContent(
    formId: Int!
    reportDetails: String!
    documentContent: String!
    documentHeader: String
    documentFooter: String
  ): returnResolvedPlaceholderContentResponse!
}

type listDocumentTemplateResponse {
  errorCode: String
  message: String
  documentTemplateDetails: [documentTemplateDetails]
}

type documentTemplateDetails {
  documentTemplateId: Int
  title: String
  registeredBusinessAddress: Int
  templateContent: String
  formId: Int
  formName: String
  category: String
  reportId: String
  documentAttachment: Int
  generateDocForOnboarding: Int
  documentSubTypeId: Int
  description: String
  pageScope: String
  templateHeader: String
  templateFooter: String
  customFooterId: Int
  customHeaderId: Int
}

type templateInDropDownDetails {
  documentTemplateId: Int
  title: String
  registeredBusinessAddress: Int
  templateContent: String
  documentAttachment: Int
  documentSubTypeId: Int
  pageScope: String
  templateHeader: String
  templateFooter: String
  formId: Int
  formName: String
  entity: [String]
  reportId: String
  candidateTemplate: String
  employeeTemplate: String
}

type listEmployeeGeneratedDocumentsResponse {
  errorCode: String
  message: String
  documentDetails: [generatedDocumentDetails]
}

type generatedDocumentDetails {
  generatedDocumentId: Int
  templateId: Int
  templateName: String
  documentName: String
  employeeId: Int
  employeeName: String
  personalEmail: String
  employeeEmail: String
  mobileNumber: String
  userDefinedEmployeeId: String
  candidateId: String
  candidateName: String
  userDefinedCandidateId: String
  candidatePersonalEmail: String
  candidateJobEmail: String
  candidateMobileNumber: String
  documentContent: String
  registeredBusinessAddress: String
  authorizedSignatories: String
  documentLink: String
  status: String
  addedBy: String
  updatedBy: String
  addedOn: String
  updatedOn: String
}

type listTemplatesInDropdownResponse {
  errorCode: String
  message: String
  documentTemplateDetails: [templateInDropDownDetails]
}

type listTemplateCustomComponentsResponse {
  errorCode: String
  message: String
  templateCustomComponents: String
}

type listDocumentTemplateFieldsResponse {
  errorCode: String
  message: String
  documentTemplateFieldDetails: String
}

type retrieveDocumentInputsResponse {
  errorCode: String
  message: String
  documentDetails: String
  businessAddress: businessAddressDetails
}

type listEmployeesInGeneratorResponse {
  errorCode: String
  message: String
  employeeDetails: [employeeDetails]
}

type employeeDetails {
  employeeId: Int
  userDefinedEmployeeId: String
  employeeName: String
  designationName: String
  designationCode: String
}

type businessAddressDetails {
  street1: String
  street2: String
  cityId: String
  stateId: String
  countryCode: String
  pincode: String
  state: String
  city: String
  country: String
}

type commonResponse {
  errorCode: String
  message: String
}

type listCandidatesResponse {
  errorCode: String
  message: String
  candidateDetails: [candidateDetails]
  recruitmentSettings: recruitmentSettings
}

type candidateDetails {
  candidateId: Int
  candidateName: String
  Salary_Exist: String
  blacklisted: String
  status: String
}

type recruitmentSettings {
  Enforce_Candidate_Salary: Int
}

type retrieveAccreditationDetailsResponse {
  errorCode: String
  message: String
  accreditationDetails: [accreditationDetails]
}

type accreditationDetails {
  accreditationDetailId: Int!
  accreditationCategoryAndTypeId: Int!
  accreditationCategory: String!
  accreditationType: String!
  fileName: String!
  receivedDate: String
  expiryDate: String
  identifier: String
  employeeId: Int!
  verified: String
  verifiedDate: String
  employeeName: String
  managerName: String
  serviceProviderName: String
  designationId: Int
  departmentId: Int
  workScheduleId: Int
  employeeTypeId: Int
  locationId: Int
  empStatus: String
  employeeType: String
  Job_Role_Ids:[Int]
  Job_Role_Details: [Job_Role_Details]
}
type Job_Role_Details{
  Job_Role_Name:String
  Job_Role_Id:Int
}
type listCustomComponentsResponse {
  errorCode: String
  message: String
  customComponentsDetails: String
}

type listClauseComponentsResponse {
  errorCode: String
  message: String
  clauseComponentsDetails: String
}

type clauseComponentDetails {
  clauseId: Int
  clauseName: String
  clauseContent: String
  clausePlaceholder: String
  clauseHolder: String
}

type retrieveDocumentDetailsResponse {
  errorCode: String
  message: String
  documentDetails: String
}

type returnResolvedPlaceholderContentResponse {
  errorCode: String
  message: String
  resolvedContent: String
  resolvedHeader: String
  resolvedFooter: String
}

schema {
  query: Query
}
