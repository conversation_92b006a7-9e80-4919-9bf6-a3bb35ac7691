# You can override the included template(s) by including variable overrides
# See https://docs.gitlab.com/ee/user/application_security/sast/#customizing-the-sast-settings
# Note that environment variables can be set in several places
# See https://docs.gitlab.com/ee/ci/variables/#priority-of-environment-variables
image: node:18
stages:
- Code_Quality
- SAST
- develop
- release
- production

sast:
  stage: SAST
  variables:
    SAST_EXCLUDED_PATHS: spec, test, tests, tmp, dist

code_quality:
  stage: Code_Quality

code_quality_html:
  extends: code_quality
  variables:
    REPORT_FORMAT: html
  artifacts:
    paths: [gl-code-quality-report.html]

include:
- template: Code-Quality.gitlab-ci.yml
- template: Security/SAST.gitlab-ci.yml

docusign-deploy to develop:
  only:
  - develop
  stage: develop
  script:
  - cd docuSign
  - npm install -g serverless@3.38.0
  - npm install
  - sls deploy --stage dev --region ap-south-1 --verbose
  when: manual
  environment: dev
docusign-deploy to hrapp:
  only:
  - master
  stage: production
  script:
  - cd docuSign
  - npm install -g serverless@3.38.0
  - npm install
  - sls deploy --stage prod --region ap-south-1 --verbose
  when: manual
  environment: prod-hrapp
docusign-deploy to cannyhr:
  only:
  - master
  stage: production
  script:
  - cd docuSign
  - npm install -g serverless@3.38.0
  - npm install
  - sls deploy --stage cannyhr --region ap-south-1 --verbose
  environment: prod-cannyhr
  when: manual
