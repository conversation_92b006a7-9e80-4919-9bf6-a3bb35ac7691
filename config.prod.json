{"securityGroupIds": ["sg-0d3854ad69d6e7e09", "sg-0a9a621d864c23783"], "subnetIds": ["subnet-075680669427eff9d", "subnet-0e2510550b4a177a5"], "dbSecretName": "prod/hrapp/pgaccess", "region": "ap-south-1", "lambdaRole": "arn:aws:iam::484056187456:role/LambdaMicroservice", "dbPrefix": "hrapp_", "domainName": "hrapp", "authorizerARN": "arn:aws:lambda:ap-south-1:484056187456:function:ATS-prod-firebaseauthorizer", "customDomainName": "api.hrapp.co", "logoBucket": "s3.logos.hrapp.co", "documentsBucket": "s3.taxdocs.hrapp.co", "emailFrom": "<EMAIL>", "sesTemplatesRegion": "us-west-2", "webAddress": ".co", "emailTo": "<EMAIL>", "sesRegion": "us-west-2", "awsChromeLambdaLayerARN": "arn:aws:lambda:ap-south-1:764866452798:layer:chrome-aws-lambda:25", "encryptedDocBucket": "documents.hrapp.co", "atsNameForIndeed": "Flowtrack"}