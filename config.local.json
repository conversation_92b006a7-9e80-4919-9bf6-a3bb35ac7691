{"dbSecretName": "hrapp-stage", "region": "ap-south-1", "lambdaRole": "arn:aws:iam::692647644057:role/lambdaFullAccess", "dbPrefix": "hrapp_", "domainName": "hrapp", "authorizerARN": "arn:aws:lambda:ap-south-1:692647644057:function:firebase-lambda-authorizer", "customDomainName": "", "securityGroupIds": "", "subnetIds": "", "logoBucket": "s3.hrapp-dev-public-images", "documentsBucket": "caprice-dev-stage", "emailFrom": "<EMAIL>", "sesTemplatesRegion": "us-east-1", "webAddress": ".co.in", "emailTo": "<EMAIL>", "sesRegion": "us-east-1", "awsChromeLambdaLayerARN": "arn:aws:lambda:ap-south-1:764866452798:layer:chrome-aws-lambda:25", "encryptedDocBucket": "documents.hrapp.co.in", "atsNameForIndeed": "Flowtrack"}