{"securityGroupIds": ["sg-06e06ee52057fb09f"], "subnetIds": ["subnet-023ff1fb8431b273f", "subnet-09dd9cf2a9239643c"], "dbSecretName": "PROD/CANNY/PGACCESS", "region": "ap-south-1", "lambdaRole": "arn:aws:iam::378423228887:role/lambdaFullAccess", "dbPrefix": "cannyhr_", "domainName": "cannyhr", "authorizerARN": "arn:aws:lambda:ap-south-1:378423228887:function:ATS-cannyhr-firebaseauthorizer", "customDomainName": "api.cannyhr.com", "logoBucket": "s3.logos.cannyhr.com", "documentsBucket": "s3.taxdocs.cannyhr.com", "emailFrom": "<EMAIL>", "sesTemplatesRegion": "us-east-1", "webAddress": ".com", "emailTo": "<EMAIL>", "sesRegion": "us-west-2", "awsChromeLambdaLayerARN": "arn:aws:lambda:ap-south-1:764866452798:layer:chrome-aws-lambda:25", "encryptedDocBucket": "documents.cannyhr.com", "atsNameForIndeed": "Cannyhr"}