{"securityGroupIds": ["sg-098a0324de5632c9e", "sg-03b2b753038f67293"], "subnetIds": ["subnet-020dff85cddc0752e", "subnet-0b1a6fbc2c7b782ad"], "dbSecretName": "hrapp-stage", "region": "ap-south-1", "lambdaRole": "arn:aws:iam::692647644057:role/lambdaFullAccess", "dbPrefix": "hrapp_", "domainName": "hrapp", "authorizerARN": "arn:aws:lambda:ap-south-1:692647644057:function:ATS-dev-firebaseauthorizer", "customDomainName": "api.hrapp.co.in", "logoBucket": "s3.hrapp-dev-public-images", "documentsBucket": "caprice-dev-stage", "emailFrom": "<EMAIL>", "sesTemplatesRegion": "us-east-1", "webAddress": ".co.in", "emailTo": "<EMAIL>", "sesRegion": "us-west-2", "awsChromeLambdaLayerARN": "arn:aws:lambda:ap-south-1:764866452798:layer:chrome-aws-lambda:25", "encryptedDocBucket": "documents.hrapp.co.in", "atsNameForIndeed": "Flowtrack"}